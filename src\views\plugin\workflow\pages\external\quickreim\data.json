{"ePdf": ["http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAPAxVAAD2oL9395o035.png", "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAQ_VvAACmlWLBDr4383.pdf"], "data": [{"invoiceDetail": [{"photoList": [{"iid": 1046193, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAPAxVAAD2oL9395o035.png", "aid": 428911}], "invoiceTypeNew": "1", "invoiceAmount": 100, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 29, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "other", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "012002300411", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "2024-07", "cardId": "", "pocketName": "南京出差", "taxAmount": 0, "departurePlace": "", "iid": 1046193, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "其他（地铁客车出租车等）", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1046193, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAQ_VvAACmlWLBDr4383.pdf", "aid": 428912}], "invoiceNumber": "53029820", "currency": "CNY", "reimbursementName": "金小琪-240808-2", "subjectName": "电脑补助", "invoicePurpose": "7月份电脑补助", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 14:16", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046182, "linkIid": 1046193, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}], "reimbursement_amount": 100, "user_name": "金小琪", "actual_amount": 100, "sign": 1001, "initial_amount": 100, "reimbursement_date": "2024-08-08", "sid": 29, "voiceList": [1046193], "voiceCount": 1, "create_date": "2024-08-08 16:12:44", "reimbursement_purpose": "7月份电脑补助", "link_type_id": 1046193, "revisability": false, "computerFlag": "0", "rbmDate": "", "business_subsidy": 0, "link_type": "0", "bdid": 1123691, "consolidated": 0, "month": "2024-07", "user_id": "jinxq", "subject_name": "电脑补助", "invoceAggregation": {"sumdaily": 0, "sumfounds": 0, "sumcustomer": 0, "sumcontract": 0, "sumopportunity": 0, "sumproject": 0, "sumstaff": 0}, "bid": 331814}, {"invoiceDetail": [{"photoList": [], "invoiceTypeNew": "1", "invoiceAmount": 237.1, "pid": 49867, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 21, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723132800000, "linkCode": "", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "011002301511", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "2024-07", "cardId": "", "pocketName": "默认票夹", "taxAmount": 0, "departurePlace": "", "iid": 1047164, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1047164, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/E6/wKiwDGa1iyaAaFZiAACKRK3WDBg852.pdf", "aid": 429838}], "invoiceNumber": "18263447", "currency": "CNY", "reimbursementName": "耿伟豪-240809-1", "subjectName": "手机话费", "invoicePurpose": "月份手机话费", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-09", "invoiceLinkState": 1, "userName": "耿伟豪", "userId": "gengweih", "invoiceSpecial": 0, "createTime": "2024-08-09 11:21", "linkType": "", "invoiceLinkInfo": {"linkTypeName": "", "linkCode": "", "linkId": 1047153, "linkIid": 1047164, "linkType": "", "linkDate": "", "linkName": "", "linkRemarks": ""}}], "reimbursement_amount": 237.1, "user_name": "耿伟豪", "actual_amount": 189.68, "sign": 1015, "initial_amount": 237.1, "reimbursement_date": "2024-08-09", "sid": 21, "voiceList": [1047164], "voiceCount": 1, "create_date": "2024-08-09 11:22:36", "reimbursement_purpose": "月份手机话费", "link_type_id": 1047164, "revisability": false, "computerFlag": "0", "rbmDate": "", "business_subsidy": 0, "link_type": "0", "bdid": 1124612, "consolidated": 0, "month": "2024-07", "user_id": "gengweih", "subject_name": "手机话费", "invoceAggregation": {"sumdaily": 0, "sumfounds": 0, "sumcustomer": 0, "sumcontract": 0, "sumopportunity": 0, "sumproject": 0, "sumstaff": 0}, "bid": 331996}], "proportion": "", "amountLinkSelf": 0, "isPostSaleProject": false, "presale": false, "currentByUserId": {"current": 1000, "transfer": 0, "subtract": 2000, "preceding": 1000, "isGJ": false}, "LogList": [], "assigneeMessage": [{"processInstanceId": "20991377", "messageParts": ["同意。"], "action": "AddComment", "fullMessage": "同意。", "fullMessageBytes": "5ZCM5oSP44CC", "persistentState": "org.activiti.engine.impl.persistence.entity.CommentEntity", "id": "20998502", "time": 1723124336000, "message": "同意。", "type": "comment", "userId": "安琪", "taskId": "20991400"}, {"processInstanceId": "20991377", "messageParts": ["同意。"], "action": "AddComment", "fullMessage": "同意。", "fullMessageBytes": "5ZCM5oSP44CC", "persistentState": "org.activiti.engine.impl.persistence.entity.CommentEntity", "id": "21090120", "time": 1723524682000, "message": "同意。", "type": "comment", "userId": "王健", "taskId": "20998507"}, {"processInstanceId": "20991377", "messageParts": ["同意。"], "action": "AddComment", "fullMessage": "同意。", "fullMessageBytes": "5ZCM5oSP44CC", "persistentState": "org.activiti.engine.impl.persistence.entity.CommentEntity", "id": "21112609", "time": 1724382201000, "message": "同意。", "type": "comment", "userId": "王佺", "taskId": "21090123"}, {"processInstanceId": "20991377", "messageParts": ["同意(其他审批环节已同意,系统默认通过)"], "action": "AddComment", "fullMessage": "同意(其他审批环节已同意,系统默认通过)", "fullMessageBytes": "5ZCM5oSPKOWFtuS7luWuoeaJueeOr+iKguW3suWQjOaEjyzns7vnu5/pu5jorqTpgJrov4cp", "persistentState": "org.activiti.engine.impl.persistence.entity.CommentEntity", "id": "21112614", "time": 1724382202000, "message": "同意(其他审批环节已同意,系统默认通过)", "type": "comment", "userId": "王佺", "taskId": "21112613"}], "approvalPerson": ["安琪", "王健", "万云涛", "吕兴海"], "showSales": false, "rbtReimbusement": {"newType": 0, "marketingDirector": "", "isElectronic": 0, "userCode": "销售三部-5617", "employeeNumber": "5617", "businessPlace": "", "isLinked": 0, "reimbursementType": "rc", "result": 0, "revocation": 0, "reimbursementDesc": "7月电脑补助", "businessSubsidy": 0, "client": 1, "currency": "CNY", "reimbursementAmount": 100, "reimbursementTypeName": "日常报销", "reimbursementName": "金小琪-240808-2", "approvalStage": "常务副总裁", "marketingDirectorName": "", "approvalState": 1, "userName": "金小琪", "currentAssignee": "周曙", "userId": "jinxq", "createTime": 1723104823000, "processInstId": "20991377", "submissionorder": 2, "businessDay": 0, "position": "销售助理", "bid": 331814, "contracPlace": "1"}, "userState": 1, "isArea": false, "redisKey": "jinxq:240808-2", "invoiceMessage": [{"photoList": [{"iid": 1046193, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAPAxVAAD2oL9395o035.png", "aid": 428911}], "invoiceTypeNew": "1", "invoiceAmount": 100, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 29, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "other", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "012002300411", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "2024-07", "cardId": "", "pocketName": "南京出差", "taxAmount": 0, "departurePlace": "", "iid": 1046193, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "其他（地铁客车出租车等）", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1046193, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAQ_VvAACmlWLBDr4383.pdf", "aid": 428912}], "invoiceNumber": "53029820", "currency": "CNY", "reimbursementName": "金小琪-240808-2", "subjectName": "电脑补助", "invoicePurpose": "7月份电脑补助", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 14:16", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046182, "linkIid": 1046193, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}], "kjrcwb": 0, "afterSale": false, "certificatesMessage": [], "consolidatedInvoices": []}