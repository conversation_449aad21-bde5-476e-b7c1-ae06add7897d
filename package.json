{"name": "saber", "version": "4.3.0", "scripts": {"dev": "vite --host", "prod": "vite --mode production", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "serve": "vite preview --host"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@nutflow/nf-design-elp": "^1.4.0", "@saber/nf-design-base-elp": "^1.3.0", "@saber/nf-form-design-elp": "^1.4.0", "@saber/nf-form-elp": "^1.4.0", "@smallwei/avue": "^3.5.6", "animate.css": "^4.1.1", "avue-plugin-ueditor": "^1.0.3", "axios": "^0.21.1", "codemirror": "^5.65.16", "crypto-js": "^4.1.1", "dayjs": "^1.10.6", "echarts": "^5.5.1", "element-plus": "^2.8.7", "highlight.js": "^11.9.0", "js-base64": "^3.7.4", "js-cookie": "^3.0.0", "js-md5": "^0.7.3", "nprogress": "^0.2.0", "sm-crypto": "^0.3.13", "vue": "^3.4.27", "vue-i18n": "^9.1.9", "vue-router": "^4.3.2", "vue3-clipboard": "^1.0.0", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.27", "autoprefixer": "^10.4.21", "prettier": "^2.8.7", "sass": "^1.77.2", "terser": "^5.31.1", "unplugin-auto-import": "^0.11.2", "vite": "^5.2.12", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}