// 全局变量
@import './variables.scss';

/* 全局覆盖 Element UI 的字体大小 */  


// /* 针对 Element UI 组件的样式覆盖 */  
.el-button,  
.el-input,  
.el-select,  
.el-table th,  
.el-table td,  
.el-pagination {  
  font-size: 16px; /* 组件的字体大小 */  
} 
/* 修改 el-tree 组件的字体大小 */  
.el-tree {  
  font-size: 16px; /* 适当调整为你希望的大小 */  
}  

/* 修改 el-tree 节点的字体样式 */  
.el-tree .el-tree-node {  
  font-size: 16px; /* 或者其他需要的大小 */  
} 


.el-form-item__label {  
  // font-size: 16px; /* 适当调整大小 */  
}  


// /* 修改 el-menu 中菜单项的字体大小 */  
.el-menu span{  
  font-size: 18px; /* 适当调整为你希望的大小 */  
}  
// span{
//   font-size: 16px;
// }
// /* 修改 el-menu-item 的样式 */  
// .el-menu-item {  
//   line-height: 50px; /* 确保行高合适 */  
// }  

// /* 修改 el-tabs 的字体大小 */  
// .el-el-tabs__nav-wrap {  
//   font-size: 20px; /* 适当调整字体大小 */  
// }  

// /* 修改 el-tab-pane 的内容字体大小 */  
// .el-tabs__content {  
//   font-size: 16px; /* 适当调整内容字体大小 */  
// }


// /* 修改 el-input 输入框中的文字大小 */  
// .el-input__inner {  
//   font-size: 20px; /* 适当调整为您希望的大小 */  
// }  


a {
  text-decoration: none;
  color: #333;
}

* {
  outline: none;
}

.avue-sidebar,
.avue-top,
.avue-logo,
.avue-layout
.login-logo,
.avue-main {
  transition: all .3s;
}

.avue-layout {
  display: flex;
  height: 100%;
  overflow: hidden;

  &--horizontal {
    flex-direction: column;

    .avue-sidebar {
      width: 100%;
      height: $top_height;
      display: flex;

      .avue-menu, .el-menu-item, .el-sub-menu__title {
        height: $top_height;
        line-height: $top_height;
      }

      .is-active:before {
        display: none;
      }
    }

    .avue-logo {
      width: $sidebar_width
    }
  }
}

.avue-contail {
  width: 100%;
  height: 100%;
  background: #f0f2f5;
  background-size: 100%;
  background-repeat: no-repeat;
}

.avue--collapse {
  .avue-sidebar,
  .avue-logo {
    width: $sidebar_collapse;
  }
}

.avue-main {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
  background: #f0f2f5;
}

#avue-view {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 10px;
}

.avue-view {
  width: 100%;
  box-sizing: border-box;
}

.avue-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  text-align: center;

  .copyright {
    color: #666;
    font-size: 12px;
  }
}

.mac_bg {
  background-image: url("/img/bg.jpg");
  background-color: #000;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

// ele样式覆盖
@import './element-ui.scss';
// 顶部右侧显示
@import './top.scss';
// 导航标签
@import './tags.scss';
// 工具类函数
@import './mixin.scss';
// 侧面导航栏
@import './sidebar.scss';
//主题
@import './theme/index.scss';
//通用配置
@import './normalize.scss';
//图标配置
@import './iconfont.scss';
//登录样式
@import "./login.scss";
//适配
@import './media.scss';
//滚动条样式
@include scrollBar;

