<template>
  <div class="invoice_detail">
    <div class="title_header_block">发票信息</div>
    <div class="item">
      <div v-if="invoiceInfo.subjectId != 2">开票日期：{{ invoiceInfo.invoiceDate }}</div>
      <div>科目：{{ invoiceInfo.subjectName }}</div>
      <div v-if="invoiceInfo.subjectId == 12">
        证书类型：
        <span v-if="invoiceInfo.certType == '1'">新证</span>
        <span v-if="invoiceInfo.certType == '2'">续证</span>
        <span v-if="invoiceInfo.certType == '3'">无证书</span>
        <span v-if="invoiceInfo.certType == null || invoiceInfo.certType == ''">--</span>
      </div>
      <div v-if="invoiceInfo.subjectId == 12">资质名称：{{ invoiceInfo.feename }}</div>
      <div>发票金额：{{ invoiceInfo.invoiceAmount }}</div>
      <div
        v-if="
          invoiceInfo.subjectId == 20 || invoiceInfo.subjectId == 2 || invoiceInfo.subjectId == 29
        "
      >
        交通工具：{{ invoiceInfo.vehicle }}
      </div>
      <div>发票代码：{{ invoiceInfo.invoiceCode ? invoiceInfo.invoiceCode : '--' }}</div>
      <div>发票号码：{{ invoiceInfo.invoiceNumber ? invoiceInfo.invoiceNumber : '--' }}</div>
      <div v-if="invoiceInfo.subjectId != 2">
        是否专票：<el-checkbox
          v-model="invoiceInfo.invoiceSpecial"
          :disabled="invoiceInfo.invoiceLinkState == 1"
        ></el-checkbox>
      </div>
      <div v-if="invoiceInfo.subjectId == 21 || invoiceInfo.subjectId == 29">
        报销月份：{{ invoiceInfo.month }}
      </div>
      <div>单据数量：{{ invoiceInfo.sheetsNum }}</div>
      <div>
        发票用途：<span
          :class="
            ComputerActive(invoiceInfo.invoicePurpose) &&
            (invoiceInfo.subjectName == '市内交通费' || invoiceInfo.subjectName == '差旅交通费')
              ? 'info-right-active'
              : ''
          "
          >{{ invoiceInfo.invoicePurpose }}</span
        >
      </div>
      <div v-if="invoiceInfo.subjectId == 2 && invoiceInfo.goOff && invoiceInfo.arrivalTime">
        出发到达时间：{{ rules(invoiceInfo.goOff) }}~{{ rules(invoiceInfo.arrivalTime) }}
      </div>
      <div
        v-if="
          invoiceInfo.subjectId == 2 && (invoiceInfo.departurePlace || invoiceInfo.arrivalPlace)
        "
      >
        出发到达地点：{{ invoiceInfo.departurePlace }}~{{ invoiceInfo.arrivalPlace }}
      </div>
      <div>所属票夹：{{ invoiceInfo.pocketName }}</div>
      <div v-if="invoiceInfo.invoiceLinkInfo">
        关联类别：{{ this.leibie.domainValueDesc ? this.leibie.domainValueDesc : '--' }}
      </div>
      <div v-if="invoiceInfo.invoiceLinkInfo">
        关联详情：{{
          invoiceInfo.invoiceLinkInfo.linkName
            ? invoiceInfo.invoiceLinkInfo.linkName !== 'undefined'
              ? invoiceInfo.invoiceLinkInfo.linkName
              : '--'
            : '--'
        }}
      </div>
      <div v-if="invoiceInfo.subjectId == 7">住宿天数：{{ invoiceInfo.dayNum }}</div>
    </div>
    <div class="item_img" style="display: flex; align-items: center">
      <ul class="pdf_block">
        <!-- <li  @click="openPdf(pdf)"> -->
        <li v-for="pdf in invoiceInfo.pdfList" :key="pdf.aid" @click="openPdf(pdf)">
          <!-- <img src="@/assets/pdf_icon.jpg" title="点击查看电子发票"/> -->
          电子发票
        </li>
      </ul>
      <el-image
        v-for="img in invoiceInfo.imgList"
        :key="img.aid"
        :src="img.invoice_photo_url"
        :preview-src-list="previewImgs"
      >
      </el-image>
    </div>

    <div
      v-if="!!invoiceInfo.invoiceSplitList && invoiceInfo.invoiceSplitList.length > 0"
      class="title_header_block"
    >
      拆分明细
    </div>
    <el-table
      v-if="!!invoiceInfo.invoiceSplitList && invoiceInfo.invoiceSplitList.length > 0"
      :data="invoiceInfo.invoiceSplitList"
      class="split_invoice_table"
      :header-cell-style="{ background: '#F5F5F5', color: '#5D5D5D' }"
      style="width: 100%; overflow: inherit"
    >
      <el-table-column label="开票日期" width="170">
        <template #default="scope">
          <span v-if="scope.row.invoiceDate">{{ scope.row.invoiceDate }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="科目" width="170">
        <template #default="scope">
          {{ scope.row.subjectName }}
        </template>
      </el-table-column>
      <el-table-column label="发票金额">
        <template #default="scope">
          <span>{{ toThousands(scope.row.invoiceAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发票用途">
        <template #default="scope">
          <span>{{ scope.row.invoicePurpose }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关联关系">
        <template #default="scope">
          <span>{{ scope.row.linkTypeName }} - {{ scope.row.linkName }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 证书明细-->
    <div v-if="invoiceInfo.subjectId == 12" class="title_header_block">证书详情</div>
    <div class="ordinaryDetails" v-if="invoiceInfo.subjectId == 12">
      <el-table
        :data="certificateDtoList"
        border
        :header-cell-style="{ background: '#F5F5F5', color: '#5D5D5D', textAlign: 'center' }"
        style="width: 100%"
        :cell-style="cellStyle"
      >
        <el-table-column label="序号" type="index" width="100%"> </el-table-column>
        <el-table-column label="发证机构">
          <template #default="scope">
            <span v-if="scope.row.firm != '其他'">{{ scope.row.firm }}</span>
            <span v-else>{{ scope.row.otherFirm }}</span>
          </template>
        </el-table-column>
        <el-table-column label="名称级别">
          <template #default="scope">
            <span v-if="scope.row.certLevel != '其他'">{{ scope.row.certLevel }}</span>
            <span v-else>{{ scope.row.otherLevel }}</span>
          </template>
        </el-table-column>
        <el-table-column label="领域方向">
          <template #default="scope">
            <span v-if="scope.row.orientation != '其他'">{{ scope.row.orientation }}</span>
            <span v-else>{{ scope.row.otherOrientation }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="getTime"
          :label="invoiceInfo.certType == '2' ? '重认证时间' : '取证时间'"
        >
          <template #default="scope">
            <span v-if="scope.row.getTime != '' && scope.row.getTime != undefined">{{
              formatDate1(scope.row.getTime)
            }}</span>
            <span v-else>{{ (scope.row.getTime = '--') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="附件内容">
          <template #default="scope">
            <el-button type="primary" size="small" v-if="scope.row.filePath">
              <!--                                    <pdf ref="pdf" src="scope.row.filePath">-->

              <!--                                    </pdf>-->
              <a :href="scope.row.filePath" target="_blank" style="color: #fff">查看</a>
            </el-button>
            <span v-else>无</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
// import { ElImage } from 'element-ui';
// import {formatDate} from "../../../common/time"
import res from './godetail.json';
import { findone } from '@/api/reim/reim.js';
export default {
  name: 'godetail',
  props: ['iid'],
  // //时间的格式化
  // filters: {
  //     formatDate1(time) {
  //         var date = new Date(time)
  //         return formatDate(date, 'yyyy-MM-dd')
  //     }
  // },
  computed: {
    invoiceSpecial() {
      return this.invoiceInfo.invoiceSpecial == 1 ? true : false;
    },
  },
  data() {
    return {
      //测试勿动
      // baseImgUrl: "http://wx.ntdhcc.com:8088/reim/filepath",
      //正式勿动
      baseImgUrl: 'https://nwechat.sino-bridge.com:8866/reim/filepath',
      invoiceInfo: {},
      dialogImageUrl: '',
      dialogVisible: false,
      previewImgs: [],
      certificateDtoList: [], //证书的相关数据
      leibie: {},
      url: 'http://wechat.sino-bridge.com:8090/reim/filepath/group1/M00/00/14/wKhxS12JrmKAXdZsAAZldLL2r3A869.jpg',
      strActive: ['电脑补助', '笔记本补助', '电脑', '笔记本'],
    };
  },
  mounted() {
    console.log(this.iid, 'ooo');
    this.getDetail();
  },
  watch: {
    iid(newValue, oldValue) {
      this.getDetail();
    },
  },
  methods: {
    getDetail() {
      findone(this.iid).then(res => {
        this.invoiceInfo = res.data.data;
        // this.invoiceInfo = res.data;
        //证书详细
        this.certificateDtoList = this.invoiceInfo.certificateDtoList;
        console.log(this.invoiceInfo, '档条数据');
        this.previewImgs = [];
        this.invoiceInfo.imgList = this.invoiceInfo.photoList.filter(item => {
          if (item.photo_type == '1') {
            // // 截取地址:8888后面，然后拼接store定义的地址
            // if (!!item.invoice_photo_url && item.invoice_photo_url.split(':8888').length > 1) {
            //   item.invoice_photo_url = this.baseImgUrl + item.invoice_photo_url.split(':8888')[1];
            // } else {
            //   item.invoice_photo_url = '';
            // }
            if (item.invoice_photo_url) {
              item.url = item.invoice_photo_url;
            } else {
              item.url = '';
            }
            this.previewImgs.push(item.invoice_photo_url);
          }
          return item.photo_type == '1';
        });
        console.log('this.invoiceInfo.imgList', this.invoiceInfo.imgList);
        // 处理pdf回显
        this.invoiceInfo.pdfList = this.invoiceInfo.photoList.filter(item => {
          if (item.photo_type == '2') {
            // // 截取地址:8888后面，然后拼接store定义的地址
            // if (!!item.invoice_photo_url && item.invoice_photo_url.split(':8888').length > 1) {
            //   item.invoice_photo_url = this.baseImgUrl + item.invoice_photo_url.split(':8888')[1];
            // } else {
            //   item.invoice_photo_url = '';
            // }
            if (item.invoice_photo_url) {
              item.url = item.invoice_photo_url;
            } else {
              item.url = '';
            }
          }
          return item.photo_type == '2';
        });
        console.log('this.invoiceInfo.pdfList', this.invoiceInfo.pdfList);
        if (this.invoiceInfo.ephotoList.length > 0) {
          for (var i = 0; i < this.invoiceInfo.ephotoList.length; i++) {
            // this.invoiceInfo.pdfList.push({
            //   invoice_photo_url:
            //     this.baseImgUrl +
            //     this.invoiceInfo.ephotoList[i].invoice_photo_url.split(':8888')[1],
            // });
            this.invoiceInfo.pdfList.push({
              invoice_photo_url: this.invoiceInfo.ephotoList[i].invoice_photo_url,
            });
          }
        }
        console.log('this.previewImgs', this.previewImgs);
        this.invoiceInfo.invoiceLinkList.forEach(item => {
          if (this.invoiceInfo.invoiceLinkInfo.linkType == item.domainValue) {
            this.leibie = item;
          }
        });
      });
    },
    //证书表单内容居中对齐
    cellStyle(row) {
      return 'text-align: center;';
    },
    //查看关键字
    ComputerActive(str) {
      console.log(str, 'str');
      return this.strActive.some(val => {
        return str.search(val) != -1;
      });
    },
    //将2019-09-17转成2019.09.17
    rules(val) {
      return val.replace(/-/g, '.');
    },
    // 凭证-pdf预览
    openPdf(val) {
      window.open(val.invoice_photo_url, '_blank');

      // var newWin = window.open('', '_blank', 'height=100,width=400,top=0,left=0,toolbar=no,menubar=no,scrollbars=no, resizable=no,location=no, status=no'); //新打开一个空窗口
      // newWin.document.write(`<TITLE>${this.invoiceInfo.subjectName}</TITLE>`)
      // newWin.document.write("<BODY style='margin: 0'></BODY>")
      // let dom = `<iframe src=${val.invoice_photo_url} width='100%' height='800px' frameborder='0' marginheight='0' marginwidth='0' seamless='seamless'></iframe>`
      // newWin.document.write(dom)
    },
    // 时间格式化
    formatDate(date, fmt) {
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
      }
      let o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
      };
      for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
          let str = o[k] + '';
          fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str));
        }
      }
      return fmt;
    },
    formatDate1(time) {
      var date = new Date(time);
      return formatDate(date, 'yyyy-MM-dd');
    },
    // 前分为显示逗号
    toThousands: function (num) {
      num = num * 1;
      var num_str = num.toFixed(2).split('.')[0];
      return (
        (num_str || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') +
        '.' +
        num.toFixed(2).split('.')[1]
      );
    },
  },
};
</script>
<style scoped lang="scss" scope>
.invoice_detail {
  width: 100%;
  height: 100%;
  margin: 0 20px !important;
  .info-right-active {
    color: red;
  }
  .item {
    display: flex;
    flex-wrap: wrap;
    line-height: 40px;
    min-width: 550px;
  }
  .item > div {
    width: 50%;
  }
  .item_img {
    .el-image {
      .el-icon-circle-close {
        color: #fff;
      }
    }
  }
  .item_img {
    .el-image {
      width: 100px;
      height: 100px;
      margin-left: 30px;
      border: 1px solid #eee;
    }
    .el-image:first-child {
      margin-left: 0;
    }
    .pdf_block {
      padding-left: 0;
      margin: 0;
      display: flex;
      li {
        list-style: none;
        line-height: 28px;
        cursor: pointer;
        color: #2885e4;
        width: 100px;
        height: 100px;
        border: 1px solid #c0ccda;
        margin-right: 8px;
        background: #f6f6f6;
        font-size: 16px;
        padding-top: 32px;
        box-sizing: border-box;
        text-align: center;
      }
      li:last-child {
        margin-right: 0;
      }
    }
  }
}
.title_header_block {
  font-size: 16px;
  padding: 20px 0 20px 0;
  display: flex;
}
.title_header_block:before {
  content: '';
  width: 3px;
  height: 16px;
  background: #4994e2;
  display: block;
  margin-right: 10px;
}
</style>
