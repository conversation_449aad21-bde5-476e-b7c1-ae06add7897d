import request from '@/axios';
const REIM_URL = '/approval-api';

/**
 * 财务报销详情
 */
export const getReimDetail = ids => {
  return request({
    url: `${REIM_URL}/reim/getDetail/${ids}`,
    method: 'get',
  });
};

export const getDepartment = params => {
  return request({
    url: `${REIM_URL}/system/getDepartment`,
    method: 'post',
    params,
  });
};
export const getRbtComputerSubsidy = name => {
  return request({
    url: `${REIM_URL}/reim/getRbtComputerSubsidy/${name}`,
    method: 'get',
  });
};
export const getRbtPhoneSubsidy = name => {
  return request({
    url: `${REIM_URL}/reim/getRbtPhoneSubsidy/${name}`,
    method: 'get',
  });
};
export const getMonth = name => {
  return request({
    url: `${REIM_URL}/reim/getMonth/${name}`,
    method: 'get',
  });
};
export const getWorkDiary = params => {
  return request({
    url: `${REIM_URL}/invoice/project/workDiary`,
    method: 'get',
    params,
  });
};
export const findone = name => {
  return request({
    url: `${REIM_URL}/invoice/findone/${name}`,
    method: 'get',
  });
};
/**
 * 财务借款详情
 */
export const getLoanDetail = ids => {
  return request({
    url: `${REIM_URL}/quick_reim/borrow/selectLoanDetail/${ids}`,
    method: 'post',
  });
};

export const getCurrencyData = params => {
  return request({
    url: `${REIM_URL}/system/getCurrency`,
    method: 'get',
    params,
  });
};
