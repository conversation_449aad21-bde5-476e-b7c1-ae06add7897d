<template>
  <nf-container>
    <el-skeleton :loading="waiting" avatar :rows="8">
      <el-affix position="top" :offset="110">
        <div class="header">
          <h3>{{ process.processDefinitionName }}</h3>
          <div style="display: flex">
            <!-- <nf-theme
              v-if="process.status != 'todo'"
              v-model="theme"
              :theme-list="themeList"
            ></nf-theme>
            <nf-form-variable :process-ins-id="process.processInstanceId"></nf-form-variable> -->
          </div>
        </div>
      </el-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div id="printBody" :class="process.status != 'todo' ? `nf-theme-custom` : ''">
              <!-- <nf-form
                v-if="
                  option &&
                  ((option.column && option.column.length > 0) ||
                    (option.group && option.group.length > 0))
                "
                v-model="form"
                :style="themeCustomStyle"
                ref="form"
                v-model:defaults="defaults"
                :option="option"
                :upload-preview="handleUploadPreview"
              >
              </nf-form> -->

              <div class="app-container">
                <div class="content">
                  <!-- 基本信息 -->
                  <div class="ordinary">
                    <div>基本信息</div>
                  </div>
                  <div class="info" style="position: relative">
                    <ul>
                      <li>
                        <div><span>报销单名称</span>:</div>
                        <span>{{ approvalInfo.reimbursementName }}</span>
                        <!-- &nbsp;<el-tag size="mini" type="success">{{
                          getCurrencyVal(approvalInfo.currency)
                        }}</el-tag> -->
                      </li>
                      <li>
                        <div><span>报销人</span>:</div>
                        <span>{{ approvalInfo.userName }}</span>
                      </li>
                      <!-- <li>
                            <div><span>部门</span>:</div><span>{{approvalInfo.userCode}}</span>
                        </li> -->
                      <li>
                        <div><span>部门</span>:</div>
                        <span>{{ department }}</span>
                      </li>
                      <li>
                        <div><span>创建日期</span>:</div>
                        <!-- <span>{{ formatTime(approvalInfo.createTime, 'YMDHM') }}</span> -->
                        <span>{{ approvalInfo.createTime }}</span>
                      </li>

                      <li>
                        <div><span>报销总额</span>:</div>
                        <span style="font-weight: 800; color: red"
                          >{{ toThousands(approvalInfo.reimbursementAmount)
                          }}{{ getCurrencyUnit(approvalInfo.currency) }}</span
                        >
                      </li>
                      <li>
                        <div><span>职位</span>:</div>
                        <span>{{ approvalInfo.position }}</span>
                      </li>
                      <li>
                        <div><span>报销类型</span>:</div>
                        <span class="purpose">{{ approvalInfo.reimbursementTypeName }}</span>
                      </li>
                      <li v-if="approvalInfo.reimbursementType == 'zl'">
                        <div><span>出差地点</span>:</div>
                        <span>{{ approvalInfo.businessPlace }}</span>
                      </li>
                      <li v-if="approvalInfo.reimbursementType == 'zl'">
                        <div><span></span></div>
                        <span></span>
                      </li>
                      <li>
                        <div><span>报销用途</span>:</div>
                        <el-tooltip
                          class="item"
                          effect="dark"
                          :content="approvalInfo.reimbursementDesc"
                          placement="top-start"
                        >
                          <span class="reimbursementDesc">{{
                            approvalInfo.reimbursementDesc
                          }}</span>
                        </el-tooltip>
                      </li>
                    </ul>
                    <!-- <img v-if="approvalInfo.isElectronic == 1" class="dz_icon" src="../../assets/dz_icon.png"/> -->
                  </div>
                  <!-- 出差补助 -->
                  <div
                    class="ordinary"
                    v-if="approvalInfo.reimbursementType == 'zl'"
                    style="margin-bottom: 20px"
                  >
                    <div style="margin-top: 10px">出差详情</div>
                  </div>
                  <div class="info zlinfo" v-if="approvalInfo.reimbursementType == 'zl'">
                    <span v-if="!isBusinessDay" class="spn"
                      >出差天数：{{ approvalInfo.businessDay }} 天</span
                    >
                    <p v-if="isBusinessDay">
                      <span>出差天数：</span>
                      <input
                        @input="setBusinessDay(approvalInfo.businessDay)"
                        v-model="approvalInfo.businessDay"
                        type="number"
                        style="
                          width: 50px;
                          border: 0;
                          outline: none;
                          box-shadow: none;
                          padding: 3px 10px;
                          border: 1px solid #ccc;
                          border-radius: 3px;
                        "
                      />
                      <span>天</span>
                    </p>
                    <span v-if="!isBusinessDay" class="spn"
                      >费用标准：{{ approvalInfo.businessStandards }}
                      {{ getCurrencyUnit(approvalInfo.currency) }} / 天</span
                    >
                    <p v-if="isBusinessDay">
                      <span>费用标准：</span>
                      <input
                        @input="setBusinessStandards(approvalInfo.businessStandards)"
                        v-model="approvalInfo.businessStandards"
                        type="number"
                        style="
                          width: 50px;
                          border: 0;
                          outline: none;
                          box-shadow: none;
                          padding: 3px 10px;
                          border: 1px solid #ccc;
                          border-radius: 3px;
                        "
                      />
                      <span>天</span>
                    </p>
                    <span class="spn"
                      >出差补助：<span style="color: red; font-weight: 800"
                        >{{ approvalInfo.businessSubsidy
                        }}{{ getCurrencyUnit(approvalInfo.currency) }}</span
                      ></span
                    >
                    <span class="spn" v-if="isShow">
                      <span class="setspn" v-if="!isBusinessDay" @click="getBusinessDay">修改</span>
                      <span class="setspn onsetspn" v-if="isBusinessDay" @click="noSet">取消</span>
                      <span class="setspn" v-if="isBusinessDay" @click="putList"> 完成</span>
                    </span>
                  </div>
                  <!-- 电脑补助 -->
                  <div class="ordinary" v-if="computerMoney > 0">
                    <div>电脑补助</div>
                  </div>
                  <div class="cpu-box" v-if="computerMoney > 0">
                    <p>
                      电脑补助可用额度：<span class="info-right-active"
                        >{{ getCurrency(approvalInfo.currency) }} {{ computerInfo.balance }}</span
                      >
                    </p>
                    <p>
                      费用标准：<span class="info-right-active"
                        >{{ getCurrency(approvalInfo.currency) }} 100.00/月</span
                      >
                    </p>
                    <p>
                      本次电脑补助合计：<span class="info-right-active"
                        >{{ getCurrency(approvalInfo.currency) }}{{ computerMoney }}
                      </span>
                    </p>
                  </div>
                  <!-- 话费补助 -->
                  <div class="ordinary" v-if="showPhone">
                    <div>话费补助</div>
                  </div>
                  <div class="cpu-box" v-if="showPhone">
                    <div>
                      话费补助额度上限：<span class="info-right-active"
                        >{{ getCurrency(approvalInfo.currency)
                        }}{{ toThousands(phoneSubsidy.balance) }}</span
                      >
                    </div>
                  </div>
                  <!-- 普通明细 -->
                  <div class="ordinary" style="display: flex; justify-content: space-between">
                    <div v-if="plainList.length > 0">普通明细</div>
                    <el-button
                      v-if="!!isRepulseCom && approvalStage == '财务复核'"
                      type="warning"
                      size="mini"
                      @click="repulseCom"
                      >打回电脑补</el-button
                    >
                  </div>
                  <div class="ordinaryDetails" v-if="plainList.length > 0">
                    <el-table :data="plainList" @expand-change="expendChange" style="width: 100%">
                      <!-- TODO: 注释合并票功能 -->
                      <!-- <el-table-column v-if="isShow" :key="Math.random()" type="selection" width="55" :selectable="selectState"></el-table-column> -->
                      <el-table-column
                        prop="reimbursement_date"
                        sortable
                        label="发生日期"
                        width="100"
                      >
                        <template #default="scope">
                          <span v-if="scope.row.sid == 20">
                            {{ scope.row.invoiceDetail[0].invoiceDate }}
                          </span>
                          <span v-else>{{ scope.row.reimbursement_date }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="subject_name"
                        width="135"
                        label="发票科目/类型"
                        sortable
                      >
                        <template #default="scope">
                          <span>
                            {{ scope.row.subject_name }}/
                            {{
                              (scope.row.invoiceDetail[0].vehicle == '' ||
                                scope.row.invoiceDetail[0].vehicle == null) &&
                              (scope.row.invoiceDetail[0].invoiceSpecial == '' ||
                                scope.row.invoiceDetail[0].invoiceSpecial == '0')
                                ? '普票'
                                : scope.row.invoiceDetail[0].invoiceSpecial == '1'
                                ? '专票'
                                : scope.row.invoiceDetail[0].vehicle != '' &&
                                  scope.row.invoiceDetail[0].vehicle != null
                                ? scope.row.invoiceDetail[0].vehicle
                                : '普票'
                            }}
                            <i
                              v-if="
                                scope.row.computerFlag != 'false' && scope.row.computerFlag != '0'
                              "
                              class="el-icon-star-on"
                            ></i>
                          </span>
                          <!-- TODO: 注释合并票功能 -->
                          <!-- <el-tag v-if="scope.row.consolidated == 1" type="warning" size="mini">{{ scope.row.number }}</el-tag> -->

                          <!-- 拆分票 -->
                          <el-tag v-if="scope.row.consolidated == 2" type="warning" size="mini"
                            >拆</el-tag
                          >
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="reimbursement_amount"
                        sortable
                        label="报销金额"
                        v-if="isShow"
                        width="100"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.reimbursement_amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="actual_amount"
                        class-name="changeMoney"
                        sortable
                        label="实报金额"
                        v-if="isShow"
                        width="100"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.actual_amount) }}</span>
                        </template>
                      </el-table-column>
                      <!--  发票代码-->
                      <el-table-column prop="invoiceCode " label="发票代码" v-if="isShow">
                        <template #default="scope">
                          <span>{{
                            scope.row.invoiceDetail[0].invoiceCode != ''
                              ? scope.row.invoiceDetail[0].invoiceCode
                              : '--'
                          }}</span>
                        </template>
                      </el-table-column>
                      <!--  发票号码-->
                      <el-table-column prop="invoiceNumber" label="发票号码" v-if="isShow">
                        <template #default="scope">
                          <span>{{
                            scope.row.invoiceDetail[0].invoiceNumber != ''
                              ? scope.row.invoiceDetail[0].invoiceNumber
                              : '--'
                          }}</span>
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="actual_amount"
                        class-name="changeMoney"
                        sortable
                        label="报销金额"
                        v-if="!isShow"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.actual_amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="税额/未税" v-if="isShow">
                        <template #default="scope">
                          <span
                            >{{
                              scope.row.invoiceDetail[0].taxAmount > 0
                                ? scope.row.invoiceDetail[0].taxAmount
                                : '--'
                            }}
                            /
                            {{
                              scope.row.invoiceDetail[0].afterTaxAmount > 0
                                ? scope.row.invoiceDetail[0].afterTaxAmount
                                : '--'
                            }}</span
                          >
                        </template>
                      </el-table-column>

                      <el-table-column label="关联">
                        <template #default="scope">
                          <template
                            v-for="(item, index) in scope.row.invoiceDetail[0].invoiceLinkList"
                            :key="index"
                          >
                            <span
                              v-if="
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkType ===
                                  item.domainValue
                              "
                              >{{
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkType ===
                                  item.domainValue
                                  ? item.domainValueDesc
                                  : ''
                              }}</span
                            >
                            <span v-else>--</span>
                          </template>
                        </template>
                      </el-table-column>
                      <el-table-column label="关联详情" class-name="invoiceLinkDetail" width="100">
                        <template #default="scope">
                          <el-tooltip
                            class="item"
                            effect="dark"
                            :content="
                              scope.row.invoiceDetail[0].invoiceLinkInfo &&
                              scope.row.invoiceDetail[0].invoiceLinkInfo.linkName
                            "
                            placement="top-start"
                          >
                            <span>
                              {{
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkName &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkName != 'undefined'
                                  ? scope.row.invoiceDetail[0].invoiceLinkInfo.linkName
                                  : '--'
                              }}
                            </span>
                            <!-- <span class="detail">
                                {{scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkType === 'link_daily'?scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkDate : scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkName != 'undefined'?scope.row.invoiceLinkInfo.linkName:''}}
                                </span> -->
                          </el-tooltip>
                        </template>
                      </el-table-column>
                      <el-table-column props="linkname" label="拆分信息">
                        <template #default="scope">
                          <span
                            v-if="
                              !!scope.row.invoceAggregation &&
                              (!!scope.row.invoceAggregation.sumcontract ||
                                !!scope.row.invoceAggregation.sumcustomer ||
                                !!scope.row.invoceAggregation.sumdaily ||
                                !!scope.row.invoceAggregation.sumfounds ||
                                !!scope.row.invoceAggregation.sumopportunity ||
                                !!scope.row.invoceAggregation.sumproject ||
                                !!scope.row.invoceAggregation.sumstaff)
                            "
                          >
                            <span v-if="!!scope.row.invoceAggregation.sumcontract"
                              >合同{{ scope.row.invoceAggregation.sumcontract }}<br
                            /></span>
                            <span v-if="!!scope.row.invoceAggregation.sumcustomer"
                              >客户{{ scope.row.invoceAggregation.sumcustomer }}<br
                            /></span>
                            <span v-if="!!scope.row.invoceAggregation.sumdaily"
                              >日志{{ scope.row.invoceAggregation.sumdaily }}<br
                            /></span>
                            <span v-if="!!scope.row.invoceAggregation.sumfounds"
                              >投标{{ scope.row.invoceAggregation.sumfounds }}<br
                            /></span>
                            <span v-if="!!scope.row.invoceAggregation.sumopportunity"
                              >销售机会{{ scope.row.invoceAggregation.sumopportunity }}<br
                            /></span>
                            <span v-if="!!scope.row.invoceAggregation.sumproject"
                              >项目{{ scope.row.invoceAggregation.sumproject }}<br
                            /></span>
                            <span v-if="!!scope.row.invoceAggregation.sumstaff"
                              >人员{{ scope.row.invoceAggregation.sumstaff }}</span
                            >
                          </span>
                          <span v-else>--</span>
                        </template>
                      </el-table-column>

                      <el-table-column prop="reimbursement_purpose" label="用途">
                        <template #default="scope">
                          <!-- <el-tooltip class="item" effect="dark" :content="scope.row.reimbursement_purpose" placement="top-start"> -->
                          <!-- <span class="purpose">{{ scope.row.invoiceDetail[0].invoicePurpose }}</span> -->
                          <span
                            :class="
                              ComputerActive(scope.row.reimbursement_purpose) &&
                              (scope.row.subject_name == '市内交通费' ||
                                scope.row.subject_name == '差旅交通费')
                                ? 'info-right-active'
                                : ''
                            "
                            >{{ scope.row.reimbursement_purpose }}</span
                          >
                          <!-- </el-tooltip> -->
                        </template>
                      </el-table-column>
                      <el-table-column class-name="resultEdit" width="205" label="操作">
                        <template #default="scope">
                          <p>
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-message"
                              @click="lookdetail(scope.$index, scope.row)"
                              >详情</el-button
                            >
                          </p>
                          <p v-if="isShow" style="margin-left: 10px">
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-edit"
                              @click="getInvoice(scope.row, scope.$index)"
                              >修改</el-button
                            >
                          </p>
                          <p v-if="isShow" style="margin-left: 10px">
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-document"
                              v-if="
                                scope.row.invoiceDetail[0].ephotoList &&
                                scope.row.invoiceDetail[0].ephotoList.length > 0
                                  ? true
                                  : false
                              "
                              @click="lookpdf(scope.$index, scope.row)"
                              >pdf</el-button
                            >
                          </p>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div
                      style="text-align: right; margin-top: 5px"
                      v-if="!(!plainTaxSum && !plainAfterTaxSum) && isShow"
                    >
                      税额：{{ getCurrency(approvalInfo.currency)
                      }}{{ toThousands(plainTaxSum) }}&nbsp;&nbsp;&nbsp;未税：{{
                        getCurrency(approvalInfo.currency)
                      }}{{ toThousands(plainAfterTaxSum) }}
                      <el-button plain type="primary" size="mini" @click="openTaxSum"
                        >按科目查询</el-button
                      >
                    </div>
                  </div>
                  <!-- 证书明细 -->
                  <div class="ordinary" v-if="certificateDtoList.length > 0">
                    <div>证书明细</div>
                  </div>
                  <div class="ordinaryDetails" v-if="certificateDtoList.length > 0">
                    <el-table
                      :data="certificateDtoList"
                      border
                      :header-cell-style="{
                        background: '#F5F5F5',
                        color: '#5D5D5D',
                        textAlign: 'center',
                      }"
                      style="width: 100%"
                      :cell-style="cellStyle"
                    >
                      <el-table-column label="序号" type="index" :index="indexMethod" width="100%">
                      </el-table-column>
                      <el-table-column label="发证机构">
                        <template #default="scope">
                          <span v-if="scope.row.firm != '其他'">{{ scope.row.firm }}</span>
                          <span v-else>{{ scope.row.otherFirm }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="名称级别">
                        <template #default="scope">
                          <span v-if="scope.row.certLevel != '其他'">{{
                            scope.row.certLevel
                          }}</span>
                          <span v-else>{{ scope.row.otherLevel }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="领域方向">
                        <template #default="scope">
                          <span v-if="scope.row.orientation != '其他'">{{
                            scope.row.orientation
                          }}</span>
                          <span v-else>{{ scope.row.otherOrientation }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="getTime" label="取证时间">
                        <template #default="scope">
                          <span v-if="scope.row.getTime != '' && scope.row.getTime != undefined">{{
                            formatDate1(scope.row.getTime)
                          }}</span>
                          <span v-else>{{ (scope.row.getTime = '--') }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="附件内容">
                        <template #default="scope">
                          <el-button type="primary" size="small" v-if="scope.row.filePath">
                            <!--                                    <pdf ref="pdf" src="scope.row.filePath">-->

                            <!--                                    </pdf>-->
                            <a :href="scope.row.filePath" target="_blank" style="color: #fff"
                              >查看</a
                            >
                          </el-button>
                          <span v-else>无</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <!-- 差旅明细 -->
                  <div class="ordinary" style="display: flex; justify-content: space-between">
                    <div v-if="travelList.length > 0">差旅明细</div>
                  </div>
                  <div class="ordinaryDetails" v-if="travelList.length > 0">
                    <el-table :data="travelList" @expand-change="expendChange" style="width: 100%">
                      <!-- TODO: 注释合并票功能 -->
                      <!-- <el-table-column v-if="isShow" :key="Math.random()" type="selection" width="55" :selectable="selectState"></el-table-column> -->
                      <el-table-column prop="reimbursement_date" sortable label="发生日期">
                        <template #default="scope">
                          <span>{{ scope.row.reimbursement_date }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="subject_name" width="120" label="发票科目/类型">
                        <template #default="scope">
                          <span>
                            {{ scope.row.subject_name }}/
                            {{
                              (scope.row.invoiceDetail[0].vehicle == '' ||
                                scope.row.invoiceDetail[0].vehicle == null) &&
                              (scope.row.invoiceDetail[0].invoiceSpecial == '' ||
                                scope.row.invoiceDetail[0].invoiceSpecial == '0')
                                ? '普票'
                                : scope.row.invoiceDetail[0].invoiceSpecial == '1'
                                ? '专票'
                                : scope.row.invoiceDetail[0].vehicle != '' &&
                                  scope.row.invoiceDetail[0].vehicle != null
                                ? scope.row.invoiceDetail[0].vehicle
                                : '普票'
                            }}
                            <i
                              v-if="
                                scope.row.computerFlag != 'false' && scope.row.computerFlag != '0'
                              "
                              class="el-icon-star-on"
                            ></i>
                          </span>
                          <!-- TODO: 注释合并票功能 -->
                          <!-- <el-tag v-if="scope.row.consolidated == 1" type="warning" size="mini">{{ scope.row.number }}</el-tag> -->

                          <!-- 拆分票 -->
                          <el-tag v-if="scope.row.consolidated == 2" type="warning" size="mini"
                            >拆</el-tag
                          >
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="reimbursement_amount"
                        sortable
                        label="报销金额"
                        v-if="isShow"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.reimbursement_amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="actual_amount"
                        class-name="changeMoney"
                        sortable
                        label="实报金额"
                        v-if="isShow"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.actual_amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="actual_amount"
                        class-name="changeMoney"
                        sortable
                        label="报销金额"
                        v-if="!isShow"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.actual_amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="税额/未税" v-if="isShow">
                        <template #default="scope">
                          <span
                            >{{
                              scope.row.invoiceDetail[0].taxAmount > 0
                                ? scope.row.invoiceDetail[0].taxAmount
                                : '--'
                            }}
                            /
                            {{
                              scope.row.invoiceDetail[0].afterTaxAmount > 0
                                ? scope.row.invoiceDetail[0].afterTaxAmount
                                : '--'
                            }}</span
                          >
                        </template>
                      </el-table-column>

                      <!-- <el-table-column label="未税"  width=100 v-if="isShow">
                            <template slot-scope="scope">
                            <span>{{scope.row.invoiceDetail[0].afterTaxAmount > 0?scope.row.invoiceDetail[0].afterTaxAmount:'--'}}</span>
                            </template>
                        </el-table-column> -->
                      <el-table-column label="起止地点">
                        <template #default="scope">
                          <span
                            >{{
                              scope.row.invoiceDetail[0].departurePlace
                                ? scope.row.invoiceDetail[0].departurePlace
                                : '--'
                            }}
                            ~
                            {{
                              scope.row.invoiceDetail[0].arrivalPlace
                                ? scope.row.invoiceDetail[0].arrivalPlace
                                : '--'
                            }}</span
                          >
                        </template>
                      </el-table-column>
                      <el-table-column label="关联">
                        <template #default="scope">
                          <template
                            v-for="(item, index) in scope.row.invoiceDetail[0].invoiceLinkList"
                            :key="index"
                          >
                            <span
                              v-if="
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkType ===
                                  item.domainValue
                              "
                              >{{
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkType ===
                                  item.domainValue
                                  ? item.domainValueDesc
                                  : ''
                              }}</span
                            >
                            <span v-else>--</span>
                          </template>
                        </template>
                      </el-table-column>
                      <el-table-column label="关联详情" class-name="invoiceLinkDetail">
                        <template #default="scope">
                          <el-tooltip
                            class="item"
                            effect="dark"
                            :content="
                              scope.row.invoiceDetail[0].invoiceLinkInfo &&
                              scope.row.invoiceDetail[0].invoiceLinkInfo.linkName
                            "
                            placement="top-start"
                          >
                            <span>
                              {{
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkName &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkName != 'undefined'
                                  ? scope.row.invoiceDetail[0].invoiceLinkInfo.linkName
                                  : '--'
                              }}
                            </span>
                            <!-- <span class="detail">
                                {{scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkType === 'link_daily'?scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkDate : scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkName != 'undefined'?scope.row.invoiceLinkInfo.linkName:''}}
                                </span> -->
                          </el-tooltip>
                        </template>
                      </el-table-column>

                      <el-table-column prop="reimbursement_purpose" label="用途">
                        <template #default="scope">
                          <!-- <el-tooltip class="item" effect="dark" :content="scope.row.reimbursement_purpose" placement="top-start"> -->
                          <!-- <span class="purpose">{{ scope.row.invoiceDetail[0].invoicePurpose }}</span> -->
                          <span
                            :class="
                              ComputerActive(scope.row.reimbursement_purpose) &&
                              (scope.row.subject_name == '市内交通费' ||
                                scope.row.subject_name == '差旅交通费')
                                ? 'info-right-active'
                                : ''
                            "
                            >{{ scope.row.reimbursement_purpose }}</span
                          >
                          <!-- </el-tooltip> -->
                        </template>
                      </el-table-column>
                      <el-table-column class-name="resultEdit" width="205" label="操作">
                        <template #default="scope">
                          <p>
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-message"
                              @click="lookdetail(scope.$index, scope.row)"
                              >详情</el-button
                            >
                          </p>
                          <!-- @click.native="handelDetail(scope.row.bdid,scope.row)" -->
                          <p v-if="isShow" style="margin-left: 10px">
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-edit"
                              @click="getInvoice(scope.row, scope.$index)"
                              >修改</el-button
                            >
                          </p>
                          <p v-if="isShow" style="margin-left: 10px">
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-document"
                              v-if="
                                scope.row.invoiceDetail[0].ephotoList &&
                                scope.row.invoiceDetail[0].ephotoList.length > 0
                                  ? true
                                  : false
                              "
                              @click="lookpdf(scope.$index, scope.row)"
                              >pdf</el-button
                            >
                          </p>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div
                      style="text-align: right"
                      v-if="!(!travelTaxSum && !travelAfterTaxSum) && isShow"
                    >
                      税额：{{ getCurrency(approvalInfo.currency)
                      }}{{ toThousands(travelTaxSum) }}&nbsp;&nbsp;&nbsp;未税：{{
                        getCurrency(approvalInfo.currency)
                      }}{{ toThousands(travelAfterTaxSum) }}
                    </div>
                  </div>
                  <!-- 住宿明细 -->
                  <div
                    class="ordinary"
                    style="display: flex; justify-content: space-between"
                    v-if="hotelList.length > 0"
                  >
                    <div @click="tabRemDetail">住宿明细</div>
                  </div>
                  <div class="ordinaryDetails" v-if="hotelList.length > 0">
                    <el-table :data="hotelList" @expand-change="expendChange" style="width: 100%">
                      <!-- TODO: 注释合并票功能 -->
                      <!-- <el-table-column v-if="isShow" :key="Math.random()" type="selection" width="55" :selectable="selectState"></el-table-column> -->
                      <el-table-column prop="reimbursement_date" sortable label="发生日期">
                        <template #default="scope">
                          <span>{{ scope.row.reimbursement_date }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="subject_name" width="120" label="发票科目/类型">
                        <template #default="scope">
                          <span>
                            {{ scope.row.subject_name }}/
                            {{
                              (scope.row.invoiceDetail[0].vehicle == '' ||
                                scope.row.invoiceDetail[0].vehicle == null) &&
                              (scope.row.invoiceDetail[0].invoiceSpecial == '' ||
                                scope.row.invoiceDetail[0].invoiceSpecial == '0')
                                ? '普票'
                                : scope.row.invoiceDetail[0].invoiceSpecial == '1'
                                ? '专票'
                                : scope.row.invoiceDetail[0].vehicle != '' &&
                                  scope.row.invoiceDetail[0].vehicle != null
                                ? scope.row.invoiceDetail[0].vehicle
                                : '普票'
                            }}
                            <i
                              v-if="
                                scope.row.computerFlag != 'false' && scope.row.computerFlag != '0'
                              "
                              class="el-icon-star-on"
                            ></i>
                          </span>
                          <!-- TODO: 注释合并票功能 -->
                          <!-- <el-tag v-if="scope.row.consolidated == 1" type="warning" size="mini">{{ scope.row.number }}</el-tag> -->

                          <!-- 拆分票 -->
                          <el-tag v-if="scope.row.consolidated == 2" type="warning" size="mini"
                            >拆</el-tag
                          >
                        </template>
                      </el-table-column>

                      <el-table-column
                        prop="reimbursement_amount"
                        sortable
                        label="报销金额"
                        v-if="isShow"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.reimbursement_amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="actual_amount"
                        class-name="changeMoney"
                        sortable
                        label="实报金额"
                        v-if="isShow"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.actual_amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="actual_amount"
                        class-name="changeMoney"
                        sortable
                        label="报销金额"
                        v-if="!isShow"
                      >
                        <template #default="scope">
                          <span>{{ toThousands(scope.row.actual_amount) }}</span>
                        </template>
                      </el-table-column>
                      <!--  发票代码-->
                      <el-table-column prop="invoiceCode " label="发票代码" v-if="isShow">
                        <template #default="scope">
                          <span>{{
                            scope.row.invoiceDetail[0].invoiceCode != ''
                              ? scope.row.invoiceDetail[0].invoiceCode
                              : '--'
                          }}</span>
                        </template>
                      </el-table-column>
                      <!--  发票号码-->
                      <el-table-column prop="invoiceNumber" label="发票号码" v-if="isShow">
                        <template #default="scope">
                          <span>{{
                            scope.row.invoiceDetail[0].invoiceNumber != ''
                              ? scope.row.invoiceDetail[0].invoiceNumber
                              : '--'
                          }}</span>
                        </template>
                      </el-table-column>

                      <el-table-column label="税额/未税" v-if="isShow">
                        <template #default="scope">
                          <span
                            >{{
                              scope.row.invoiceDetail[0].taxAmount > 0
                                ? scope.row.invoiceDetail[0].taxAmount
                                : '--'
                            }}
                            /
                            {{
                              scope.row.invoiceDetail[0].afterTaxAmount > 0
                                ? scope.row.invoiceDetail[0].afterTaxAmount
                                : '--'
                            }}</span
                          >
                        </template>
                      </el-table-column>

                      <!-- <el-table-column label="未税"  width=100 v-if="isShow">
                            <template slot-scope="scope">
                            <span>{{scope.row.invoiceDetail[0].afterTaxAmount > 0?scope.row.invoiceDetail[0].afterTaxAmount:'--'}}</span>
                            </template>
                        </el-table-column> -->
                      <el-table-column label="住宿天数">
                        <template #default="scope">
                          <span>{{
                            scope.row.invoiceDetail[0].dayNum
                              ? scope.row.invoiceDetail[0].dayNum
                              : '--'
                          }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="关联">
                        <template #default="scope">
                          <template
                            v-for="(item, index) in scope.row.invoiceDetail[0].invoiceLinkList"
                            :key="index"
                          >
                            <span
                              v-if="
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkType ===
                                  item.domainValue
                              "
                              >{{
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkType ===
                                  item.domainValue
                                  ? item.domainValueDesc
                                  : ''
                              }}</span
                            >
                            <span v-else>--</span>
                          </template>
                        </template>
                      </el-table-column>
                      <el-table-column label="关联详情" class-name="invoiceLinkDetail">
                        <template #default="scope">
                          <el-tooltip
                            class="item"
                            effect="dark"
                            :content="
                              scope.row.invoiceDetail[0].invoiceLinkInfo &&
                              scope.row.invoiceDetail[0].invoiceLinkInfo.linkName
                            "
                            placement="top-start"
                          >
                            <span>
                              {{
                                scope.row.invoiceDetail[0].invoiceLinkInfo &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkName &&
                                scope.row.invoiceDetail[0].invoiceLinkInfo.linkName != 'undefined'
                                  ? scope.row.invoiceDetail[0].invoiceLinkInfo.linkName
                                  : '--'
                              }}
                            </span>
                            <!-- <span class="detail">
                                {{scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkType === 'link_daily'?scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkDate : scope.row.invoiceLinkInfo && scope.row.invoiceLinkInfo.linkName != 'undefined'?scope.row.invoiceLinkInfo.linkName:''}}
                                </span> -->
                          </el-tooltip>
                        </template>
                      </el-table-column>

                      <el-table-column prop="reimbursement_purpose" label="用途">
                        <template #default="scope">
                          <!-- <el-tooltip class="item" effect="dark" :content="scope.row.reimbursement_purpose" placement="top-start"> -->
                          <!-- <span class="purpose">{{ scope.row.invoiceDetail[0].invoicePurpose }}</span> -->
                          <span
                            :class="
                              ComputerActive(scope.row.reimbursement_purpose) &&
                              (scope.row.subject_name == '市内交通费' ||
                                scope.row.subject_name == '差旅交通费')
                                ? 'info-right-active'
                                : ''
                            "
                            >{{ scope.row.reimbursement_purpose }}</span
                          >
                          <!-- </el-tooltip> -->
                        </template>
                      </el-table-column>
                      <el-table-column class-name="resultEdit" width="205" label="操作">
                        <template #default="scope">
                          <p>
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-message"
                              @click="lookdetail(scope.$index, scope.row)"
                              >详情</el-button
                            >
                          </p>
                          <!-- @click.native="handelDetail(scope.row.bdid,scope.row)" -->
                          <p v-if="isShow" style="margin-left: 10px">
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-edit"
                              @click="getInvoice(scope.row, scope.$index)"
                              >修改</el-button
                            >
                          </p>
                          <p v-if="isShow" style="margin-left: 10px">
                            <el-button
                              type="primary"
                              size="small"
                              icon="el-icon-document"
                              v-if="
                                scope.row.invoiceDetail[0].ephotoList &&
                                scope.row.invoiceDetail[0].ephotoList.length > 0
                                  ? true
                                  : false
                              "
                              @click="lookpdf(scope.$index, scope.row)"
                              >pdf</el-button
                            >
                          </p>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div
                      style="text-align: right"
                      v-if="!(!hotelTaxSum && !hotelAfterTaxSum) && isShow"
                    >
                      税额：{{ getCurrency(approvalInfo.currency)
                      }}{{ toThousands(hotelTaxSum) }}&nbsp;&nbsp;&nbsp;未税：{{
                        getCurrency(approvalInfo.currency)
                      }}{{ toThousands(hotelAfterTaxSum) }}
                    </div>
                  </div>
                </div>
                <!-- 项目日志 -->
                <div class="ordinary" v-if="isResult">
                  <div>项目日志</div>
                </div>

                <div class="detailTable" v-if="isResult">
                  <el-table border style="width: 100%" :data="resultList">
                    <el-table-column label="日志时间">
                      <template #default="scope">
                        <span>{{ scope.row.DiaryDate }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="开始时间">
                      <template #default="scope">
                        <span>{{ scope.row.StartTime }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="结束时间">
                      <template #default="scope">
                        <span>{{ scope.row.EndDate }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="工作位置">
                      <template #default="scope">
                        <span>{{ scope.row.WorkPlace }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="工作内容">
                      <template #default="scope">
                        <!-- <el-tooltip class="item"> -->
                        <span>{{ scope.row.Content }}</span>
                        <!-- </el-tooltip> -->
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>

              <el-card
                shadow="never"
                style="margin-top: 10px"
                header="流转信息"
                :body-style="{ padding: '20px 0' }"
                v-if="isPrintShow"
              >
                <nf-flow :flow-list="flow"></nf-flow>
              </el-card>
            </div>
          </el-card>
          <el-card shadow="never" style="margin-top: 20px" v-if="process.status == 'todo'">
            <nf-examine-form
              ref="examineForm"
              v-model:comment="comment"
              :process="process"
              @user-select="handleUserSelect"
            ></nf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <nf-flow :flow-list="flow"></nf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName == 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <nf-design ref="bpmn" style="height: 500px" :options="bpmnOption"></nf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-skeleton>
    <!-- 查看详情 -->
    <el-dialog title="查看详情" v-model="showdetail" width="60%">
      <Godetail :iid="iid"></Godetail>
    </el-dialog>
    <!-- 底部按钮 -->
     <!-- {{ buttonList }} -->
     <!-- blade-api/blade-workflow/process/detail -->
    <nf-button
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @print="handlePrint"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
      @wfFinshHandler="wfFinshHandlerChange"
    ></nf-button>
    <!-- 人员选择弹窗 -->
    <nf-user-select
      ref="user-select"
      :userSelectType="userSelectType"
      :check-type="checkType"
      :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"
    ></nf-user-select>
  </nf-container>
</template>

<script>
import NfExamineForm from '../../../components/nf-exam-form/index.vue';
import NfButton from '../../../components/nf-button/index.vue';
import NfFlow from '../../../components/nf-flow/index.vue';
import NfTheme from '../../../components/nf-theme/index.vue';
import NfFormVariable from '../../../components/nf-form-variable/index.vue';
import NfUserSelect from '../../../components/nf-user-select/index.vue';

import exForm from '../../../mixins/ex-form';
import theme from '../../../mixins/theme';
import dataJson from './dataCl.json';
import Godetail from './compontents/godetail.vue';
import {
  getReimDetail,
  getDepartment,
  getRbtComputerSubsidy,
  getRbtPhoneSubsidy,
  getMonth,
  getWorkDiary,
  getCurrencyData,
} from '@/api/reim/reim.js';
export default {
  mixins: [exForm, theme],
  components: { NfUserSelect, NfExamineForm, NfButton, NfFlow, NfTheme, NfFormVariable, Godetail },
  watch: {
    '$route.query.p': {
      handler(val) {
        if (val) {
          const param = JSON.parse(window.atob(val));
          const { taskId, processInsId, serialNumber } = param;
          console.log(param, 'param-------------------');
          if ((taskId && processInsId) || processInsId) this.getDetail(taskId, processInsId);
          if (serialNumber) this.approvalResult(serialNumber);
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      option: {
        column: [
          {
            type: 'input',
            label: '金额',
            span: 12,
            display: true,
            prop: 'money',
            readonly: true,
          },
          {
            type: 'input',
            label: '流水号',
            span: 12,
            display: true,
            prop: 'reimId',
            readonly: true,
          },
        ],
      },
      vars: ['reimId', 'userName'], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      // 基本信息
      approvalInfo: {},
      // 报销明细
      tableData: [],
      certificateDtoList: [], //证书相关的信息
      allMinData: [],
      travelList: [], //差旅明细
      hotelList: [], //住宿明细
      plainList: [],
      // 部门
      department: '北京总部-销售三部-金小琪-5617', //需要单独调接口
      isBusinessDay: false,
      computerMoney: 0,
      // 税额、未税总额
      plainTaxSum: 0,
      plainAfterTaxSum: 0,

      travelTaxSum: 0,
      travelAfterTaxSum: 0,

      hotelTaxSum: 0,
      hotelAfterTaxSum: 0,
      // 项目日志
      isResult: 0,
      resultList: [], //需要单独调接口
      // 工作日志
      LogList: [],
      //控制是否有销售无的情况
      zeroMarketing: false,
      zeroMarketing1: false,
      iszeroList: [],
      iszeroList1: [],
      listBdid: '',
      // 电脑补
      computerInfo: {
        balance: 100,
        accumulative: 100,
        state: 1,
        userid: 'jinxq',
        cid: 6719,
      }, //需要单独调接口
      showPhone: false, // 是否显示话费可用额度区块
      isShow: false, // 是否财务复核
      strActive: ['电脑补助', '笔记本补助', '电脑', '笔记本'],
      isRepulseCom: false,
      phoneSubsidy: {
        // 话费补-可用额度相关
        useAmount: 0, // 本次使用金额
        balance: 200, // 可用额度    //这个值需要单独调接口
      },
      // 报销月份
      months: [
        {
          label: '2024-12',
          value: '2024-12',
        },
        {
          label: '2024-11',
          value: '2024-11',
        },
        {
          label: '2024-10',
          value: '2024-10',
        },
      ], //需要单独调接口
      // 审批日志
      journalData: [
        {
          userId: '安琪',
          time: 1723124328000,
          fullMessage: '同意。',
        },
        {
          userId: '王健',
          time: 1723524676000,
          fullMessage: '同意。',
        },
        {
          userId: '王佺',
          time: 1724382202000,
          fullMessage: '同意。',
        },
        {
          userId: '王佺',
          time: 1724382202000,
          fullMessage: '同意(其他审批环节已同意,系统默认通过)',
        },
      ], //需要单独调接口
      // 币种
      currencylist: [
        // {
        //   unit: '元',
        //   code: '¥',
        //   currency: 'CNY',
        //   disabled: false,
        //   label: '人民币',
        // },
        // {
        //   unit: '港元',
        //   code: 'HK$',
        //   currency: 'HKD',
        //   disabled: false,
        //   label: '港币',
        // },
        // {
        //   unit: '美元',
        //   code: '$',
        //   currency: 'USD',
        //   disabled: true,
        //   label: '美元',
        // },
        // {
        //   unit: '新加坡元',
        //   code: 'S$',
        //   currency: 'SGD',
        //   disabled: true,
        //   label: '新加坡元',
        // },
        // {
        //   unit: '英镑',
        //   code: '￡',
        //   currency: 'GBP',
        //   disabled: true,
        //   label: '英镑',
        // },
        // {
        //   unit: '雷亚尔',
        //   code: 'R$',
        //   currency: 'BRL',
        //   disabled: true,
        //   label: '雷亚尔',
        // },
      ], //需要单独调接口
      // 财务详情接口返回数据
      result: {},
      showdetail: false, // 查看详情
    };
  },
  mounted() {
    
    this.getCurrencyList();
  },
  methods: {
    // 前分为显示逗号
    toThousands: function (num) {
      num = num * 1;
      var num_str = num.toFixed(2).split('.')[0];
      return (
        (num_str || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') +
        '.' +
        num.toFixed(2).split('.')[1]
      );
    },
    getCurrencyList() {
      getCurrencyData({ userName: this.$store.getters.userInfo.user_id }).then(res => {
        if (res.data.code == 1) {
          this.currencylist = res.data.data.base;
        }
      });
    },
    //获取币种
    getCurrencyUnit(val) {
      const foundOption = this.currencylist.find(option => option.currency == val);
      return foundOption ? foundOption.unit : '元';
    },
    //获取币种
    getCurrency(val) {
      const foundOption = this.currencylist.find(option => option.currency == val);
      return foundOption ? foundOption.code : '￥';
    },
    //时间戳转换为普通格式
    formatTime(value, type) {
      let dataTime = '';
      let data = new Date();
      data.setTime(value);
      let year = data.getFullYear();
      let month = data.getMonth() + 1;
      let day = data.getDate();
      let hour = data.getHours();
      let minute = data.getMinutes();
      let second = data.getSeconds();

      month < 10 ? (month = '0' + month) : month;
      day < 10 ? (day = '0' + day) : day;
      hour < 10 ? (hour = '0' + hour) : hour;
      minute < 10 ? (minute = '0' + minute) : minute;
      second < 10 ? (second = '0' + second) : second;

      if (type == 'YMD') {
        dataTime = year + '-' + month + '-' + day;
      } else if (type == 'NYR') {
        dataTime = year + '年' + month + '月' + day + '日';
      } else if (type == 'YMDHMS') {
        dataTime = year + '-' + month + '-' + day + '  ' + hour + ':' + minute + ':' + second;
      } else if (type == 'YMDHM') {
        dataTime = year + '-' + month + '-' + day + '  ' + hour + ':' + minute;
      } else if (type == 'HMS') {
        dataTime = hour + ':' + minute + ':' + second;
      } else if (type == 'YM') {
        dataTime = year + '-' + month + '-';
      }
      return dataTime; // 将格式化后的字符串输出到前端显示
    },
    // 时间格式化
    formatDate(date, fmt) {
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
      }
      let o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
      };
      for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
          let str = o[k] + '';
          fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str));
        }
      }
      return fmt;
    },

    formatDate1(time) {
      var date = new Date(time);
      return formatDate(date, 'yyyy-MM-dd');
    },
    // 财务审批表单详情
    approvalResult(serialNumber) {
      console.log(this.result, 'result--------------');
      getReimDetail(serialNumber).then(result => {
        this.approvalInfo = result.data.data.rbtReimbusement;

        this.certificateDtoList = result.data.data.certificatesMessage;
        this.tableData = result.data.data.data;
        this.allMinData = result.data.data.data;
        console.log(this.allMinData, 'this.allMinData');
        this.LogList = result.data.data.LogList;
        this.allMinData.map(val => {
          //手机话费21，电脑补助29，邮运费18时候，销售可以选择0,添加 20 市内交通费,添加 30 入职体检
          if (
            val.sid == '29' ||
            val.sid == '21' ||
            val.sid == '18' ||
            val.sid == '15' ||
            val.sid == '20' ||
            val.sid == '2' ||
            val.sid == '30'
          ) {
            // this.zeroMarketing = true;
            this.iszeroList.push(true);
            console.log(val.invoiceDetail[0].linkType + '111111111222222222');
            console.log(val.invoiceDetail[0].linkType == 'link_project');
            if (val.invoiceDetail[0].linkType == 'link_project') {
              this.projectState = true;
            }
          } else {
            // this.zeroMarketing = false;
            this.iszeroList.push(false);
          }
          if (this.iszeroList.indexOf(false) > -1) {
            this.zeroMarketing = false;
          } else {
            this.zeroMarketing = true;
          }
          //住宿费 7 差旅交通费2 市内交通费20时候，销售可以选择0,
          if (val.sid != '2' && val.sid != '7' && val.sid != '20') {
            this.iszeroList1.push(false);
          } else {
            this.iszeroList1.push(true);
            if (val.invoiceDetail[0].linkType == 'link_project') {
              this.projectState = true;
            }
          }
          if (this.iszeroList1.indexOf(false) > -1) {
            this.zeroMarketing1 = false;
          } else {
            this.zeroMarketing1 = true;
          }
          //15为福利费，为核酸检测提供无费用承担人控制
          if (val.sid == '15') {
            this.zeroCostPayer = true;
          }
          if (val.sid == '21') {
            this.showPhone = true;
          }
          if (val.subject_name == '电脑补助') {
            this.computerMoney = (Number(this.computerMoney) + Number(val.actual_amount)).toFixed(
              2
            );
          }
          if (val.subject_name == '入职体检') {
            tjnum++;
          }
          if (val.subject_name == '培训费') {
            px = true;
          }
        });
        // 计算税额、未税总额
        let sumT1 = 0;
        let sumT2 = 0;
        let sumH1 = 0;
        let sumH2 = 0;
        let sumP1 = 0;
        let sumP2 = 0;
        // 打回电脑补-是否有电脑补明细字段定义
        this.isRepulseCom = false;
        let haveOther = false;
        let haveCom = false;
        this.tableData.map(item => {
          if (item.subject_name == '差旅交通费') {
            item.invoiceDetail.length > 0 &&
              (sumT1 = Number(item.invoiceDetail[0].taxAmount) + sumT1);
            item.invoiceDetail.length > 0 &&
              (sumT2 = Number(item.invoiceDetail[0].afterTaxAmount) + sumT2);
            this.travelList.push(item);
          } else if (item.subject_name == '住宿费') {
            item.invoiceDetail.length > 0 &&
              (sumH1 = Number(item.invoiceDetail[0].taxAmount) + sumH1);
            item.invoiceDetail.length > 0 &&
              (sumH2 = Number(item.invoiceDetail[0].afterTaxAmount) + sumH2);
            this.hotelList.push(item);
          } else {
            item.invoiceDetail.length > 0 &&
              (sumP1 = Number(item.invoiceDetail[0].taxAmount) + sumP1);
            item.invoiceDetail.length > 0 &&
              (sumP2 = Number(item.invoiceDetail[0].afterTaxAmount) + sumP2);
            this.plainList.push(item);
          }
          // 打回电脑补-明细中存在电脑补，同时也存在其他科目事，显示打回电脑补按钮
          if (item.sid == 29) {
            haveCom = true;
          } else if (item.sid != 2 && item.sid != 7) {
            haveOther = true;
          }
        });
        this.approvalStage = result.data.data.rbtReimbusement.approvalStage;
        this.travelTaxSum = sumT1;
        this.travelAfterTaxSum = sumT2;
        this.hotelTaxSum = sumH1;
        this.hotelAfterTaxSum = sumH2;
        this.plainTaxSum = sumP1;
        this.plainAfterTaxSum = sumP2;
        this.waiting = false;
        const userId = {
          userId: this.approvalInfo.userId,
        };
        getDepartment(userId).then(res => {
          this.department = res.data.data;
        });
        this.workDiary(this.approvalInfo.bid, this.approvalInfo.userId);
        this.getMonths(this.approvalInfo.userId);
        this.getRbtComputer(this.approvalInfo.userId);
        this.getRbtPhone(this.approvalInfo.userId);
      });
    },
    expendChange(row) {
      this.listBdid = row.bdid;
    },
    //查看关键字
    ComputerActive(str) {
      return this.strActive.some(val => {
        return str.search(val) != -1;
      });
    },
    //查看详情
    lookdetail(index, row) {
      //查看详情的函数
      console.log(index, row, 'xiangq');
      this.iid = row.voiceList[0];
      this.showdetail = true;
      console.log(this.iid, 'iid');
    },
    // 工作日志
    workDiary(bid, userId) {
      let data = {
        bid: bid,
        userId: userId,
      };
      getWorkDiary(data).then(result => {
        console.log('工作日志....', result.data.data);
        if (result.data.data) {
          this.isResult = 1;
          this.resultList = result.data.data;
        } else {
          this.isResult = 0;
        }
      });
    },
    getMonths(name) {
      getMonth(name).then(res => {
        if (res.data.code == '1') {
          this.months = res.data.data;
        }
      });
    },
    //获取电脑补助
    getRbtComputer(name) {
      getRbtComputerSubsidy(name).then(res => {
        if (res.data.code == 1) {
          this.computerInfo = res.data.data;
        }
        console.log(this.computerInfo, '-----cpu');
      });
    },
    getRbtPhone(name) {
      getRbtPhoneSubsidy(name).then(res => {
        if (res.data.code == 1) {
          this.phoneSubsidy.balance = res.data.data.phone;
        }
      });
    },

    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        const { process, form } = res;
        const { variables, status } = process;

        let { taskForm } = form;
        const option = this.option;
        option.menuBtn = false;
        let { column, group } = option;
        if (taskForm && taskForm.length > 0) {
          const columnFilter = this.filterAvueColumn(column, taskForm, true);
          console.log(columnFilter, 'columnFilter');
          column = columnFilter.column;
          let vars = columnFilter.vars || [];

          const groupArr = [];
          if (group && group.length > 0) {
            // 处理group
            group.forEach(gro => {
              const groupFilter = this.filterAvueColumn(gro.column, taskForm, true);
              gro.column = groupFilter.column;
              vars = vars.concat(groupFilter.vars);
              if (gro.column.length > 0) groupArr.push(gro);
            });
          }
          group = groupArr;
          // this.vars = vars;
        }

        if (status != 'todo') {
          // 已办，删除字段默认值
          option.detail = true;
          if (column && column.length > 0) {
            // 处理column
            column.forEach(col => {
              if (col.type == 'dynamic')
                col.children.column.forEach(cc => {
                  delete cc.value;
                });
              delete col.value;
            });
          }

          if (group && group.length > 0) {
            // 处理group
            group.forEach(gro => {
              if (gro.column && gro.column.length > 0) {
                gro.column.forEach(col => {
                  if (col.type == 'dynamic')
                    col.children.column.forEach(cc => {
                      delete cc.value;
                    });
                  delete col.value;
                });
              }
            });
          }
        }
        option.column = column;
        option.group = group;

        for (let key in variables) {
          if (!variables[key]) delete variables[key];
        }

        if (option.column && process.variables && process.variables.serialNumber) {
          option.column.unshift({
            label: '流水号',
            prop: 'serialNumber',
            span: 24,
            detail: true,
          });
        }

        this.option = option;
        this.form = variables;
        this.waiting = false;
      });
    },
    // 审核
    handleExamine(pass) {
      this.submitLoading = true;
      console.log(this.vars, 'this.vars');
      console.log(this.form, 'this.form');
      // this.$refs.form.validate((valid, done) => {
      // if (valid) {
      const variables = {};
      this.vars.forEach(v => {
        if (!this.validatenull(this.form[v])) {
          variables[v] = this.form[v];
          if (this.form[`$${v}`]) variables[`$${v}`] = this.form[`$${v}`];
        }
      });
      console.log(this.$store.getters.userInfo, '提交表单');
      variables.userName = this.$store.getters.userInfo.user_id;
      console.log(pass, variables);

      this.handleCompleteTask(pass, variables)
        .then(() => {
          this.$message.success('处理成功');
          this.handleCloseTag('/plugin/workflow/pages/process/todo');
        })
        .catch(() => {
          done();
          this.submitLoading = false;
        });
      // } else {
      //   done();
      //   this.submitLoading = false;
      // }
      // });
    },
    // 办结
    wfFinshHandlerChange(pass) {
      this.submitLoading = true;
      console.log(this.vars, 'this.vars');
      console.log(this.form, 'this.form');
      // this.$refs.form.validate((valid, done) => {
      // if (valid) {
      const variables = {};
      this.vars.forEach(v => {
        if (!this.validatenull(this.form[v])) {
          variables[v] = this.form[v];
          if (this.form[`$${v}`]) variables[`$${v}`] = this.form[`$${v}`];
        }
      });
      console.log(this.$store.getters.userInfo, '提交表单');
      variables.userName = this.$store.getters.userInfo.user_id;
      console.log(pass, variables);

      this.handleCompleteTask(pass, variables)
        .then(() => {
          this.$message.success('处理成功');
          this.handleCloseTag('/plugin/workflow/pages/process/todo');
        })
        .catch(() => {
          done();
          this.submitLoading = false;
        });
      // } else {
      //   done();
      //   this.submitLoading = false;
      // }
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}

// .header {
//   height: 50px;
//   display: flex;
//   align-items: center;
//   background: #fff;
//   border-top: solid 1px #e6e6e6;
//   position: relative;
//   .goBack {
//     cursor: pointer;
//     span {
//       color: #297cba;
//       &:nth-child(2) {
//         font-size: 16px;
//         border-right: 1px solid #ccc;
//         padding-right: 10px;
//       }
//       i {
//         color: #297cba;
//         font-size: 16px;
//       }
//     }
//   }
//   .check {
//     padding-left: 10px;
//     font-size: 16px;
//   }
// }
.dialog {
  .info {
    width: 100%;
    padding: 0 30%;
    box-sizing: border-box;
    margin-top: -30px;
    margin-bottom: -30px;
    p {
      display: flex;
      align-items: center;
      margin: 10px 0;
      .lable-info {
        width: 110px;
      }
      .info-right {
        display: inline-block;
        flex: 1;
      }
    }
  }
}
// .dialog-footer{
//   margin-top: -30px !important;
// }

.detail {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.info-right-active {
  color: red;
}
.cpu-box {
  width: 100%;
  padding: 0 200px 0 15px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.zlinfo {
  width: 100%;
  display: flex;
  padding: 10px 10px;
  box-sizing: border-box;
  align-items: center;
  font-size: 16px;
  color: #333;
  border: 1px solid #eee;
  margin: 20px 0;
  .spn,
  p {
    display: inline-block;
    flex: 1;
  }
  .setspn {
    color: #fff;
    padding: 7px 17px;
    border-radius: 5px;
    display: inline-block;
    background: #409eff;
  }
  .onsetspn {
    background: #e6a23c;
    margin-right: 15px;
  }
}
.app-container {
  width: 96%;
  height: 100%;
  // margin: 20px auto;
  padding: 0;
  background: #fff;
}
.content {
  padding: 0;
  .essentialInfo {
    width: 100%;
    font-size: 12px;
    font-weight: 600;
    color: #000;
    div {
      height: 40px;
      line-height: 40px;
    }
  }
  .info {
    ul {
      padding: 0;
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #ebeef5;
      li {
        width: 33.3%;
        padding: 0;
        margin: 0;
        list-style-type: none;
        display: flex;
        align-items: center;
        font-size: 16px;
        & > div {
          padding-left: 20px;
          font-size: 16px;
          display: flex;
          width: 100px;
          padding: 10px;
          & > span {
            text-align-last: justify;
            flex: 1;
            display: inline-block;
          }
        }
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5),
        &:nth-child(6) {
          border-bottom: 1px solid #ebeef5;
        }
        &:last-child {
          width: 100%;
          border-top: 1px solid #ebeef5;
          div {
            padding: 10px;
          }
        }
      }
    }
  }
  .ordinary {
    font-size: 12px;
    color: #000;
    display: flex;
    margin: 0;
    div {
      padding: 12px 15px;
      border-radius: 5px;
      font-weight: 600;
      margin-right: 20px;
    }
    .ordinaryActive {
      color: #fff;

      background: #409eff;
    }
  }
  .computer_block {
    display: flex;
    justify-content: space-between;
    width: 60%;
    margin-left: 15px;
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 30px;
    span {
      color: red;
    }
  }
  .approvalLog {
    width: 100%;
    font-size: 12px;
    font-weight: 600;
    color: #000;
    div {
      height: 40px;
      line-height: 40px;
    }
  }
}
.select {
  span {
    color: #000;
    font-size: 12px;
    font-weight: 600;
  }
}
.afterSale {
  .select {
    display: flex;
    align-items: center;
    margin: 15px 0;
    span {
      color: #000;
      font-size: 12px;
      font-weight: 600;
    }
  }
}
.ordinaryDetails {
  border: 1px #ebeef5 solid;
  border-bottom: 0;
  &:last-child {
    border-bottom: 0;
  }
  .el-tag.el-tag--warning {
    background-color: #ffe6c2;
    border-color: #ffe9d6;
    color: #bf7507;
  }
}
.ordinaryDetail {
  margin-bottom: 15px;
}
.result {
  width: 95%;
  margin: 20px auto;
  .btn {
    width: 90px;
  }
}
.purpose {
  display: inline-block;
  width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.active {
  border: 1px solid blue !important;
  background: blue;
}
.taxBox {
  display: inline-block;
  width: 10px;
  height: 10px;
  border: 1px solid #ccc;
}
.reimbursementDesc {
  display: inline-block;
  width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dz_icon {
  right: -10px;
  top: -50px;
  position: absolute;
  width: 80px;
  transform: rotate(20deg);
}
</style>
