import request from '@/axios';
const UNFIY_URL = '/oa-api';
// /**
//  * token换取用户信息
//  */
export const verifyToken = params => {
  console.log(params, 'params');
  return request({
    url: `${UNFIY_URL}/verifyToken`,
    method: 'post',
    params,
  });
};

// export const verifyToken = param => {
//   return new Promise(resolve => {
//     let headerParams = {
//       headers: {
//         'Content-Type': 'application/x-www-form-urlencoded',
//       },
//     };
//     request.post(`${UNFIY_URL}/verifyToken`, param, headerParams).then(res => {
//       resolve(res);
//     });
//   });
// };
