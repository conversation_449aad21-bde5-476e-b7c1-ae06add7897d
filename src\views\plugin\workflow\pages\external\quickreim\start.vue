<template>
  <nf-container>
    <el-skeleton :loading="waiting" avatar :rows="8">
      <h3 style="margin-bottom: 20px">{{ process.name }}</h3>
      <el-card shadow="never" style="margin-top: 20px">
        <!-- 自定义表单区域 -->
        <!-- <nf-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          v-model:defaults="defaults"
          @submit="handleSubmit"
          @error="loading = false"
          :upload-preview="handleUploadPreview"
        >
        </nf-form> -->
        <div class="app-container">
          <div class="content">
            <!-- 基本信息 -->
            <div class="ordinary">
              <div>基本信息</div>
            </div>
            <div class="info" style="position: relative">
              <ul>
                <li>
                  <div><span>报销单名称</span>:</div>
                  <span>{{ approvalInfo.reimbursementName }}</span>&nbsp;<el-tag size="mini" type="success">{{
                    getCurrencyVal(approvalInfo.currency)
                  }}</el-tag>
                </li>
                <li>
                  <div><span>报销人</span>:</div>
                  <span>{{ approvalInfo.userName }}</span>
                </li>
                <!-- <li>
                            <div><span>部门</span>:</div><span>{{approvalInfo.userCode}}</span>
                        </li> -->
                <li>
                  <div><span>部门</span>:</div>
                  <span>{{ department }}</span>
                </li>
                <li>
                  <div><span>创建日期</span>:</div>
                  <!-- <span>{{ formatTime(approvalInfo.createTime, 'YMDHM') }}</span> -->
                  <span> {{ approvalInfo.createTime }} </span>
                </li>

                <li>
                  <div><span>报销总额</span>:</div>
                  <span style="font-weight: 800; color: red">{{ toThousands(approvalInfo.reimbursementAmount)
                  }}{{ getCurrencyUnit(approvalInfo.currency) }}</span>
                </li>
                <li>
                  <div><span>职位</span>:</div>
                  <span>{{ approvalInfo.position }}</span>
                </li>
                <li>
                  <div><span>报销类型</span>:</div>
                  <span class="purpose">{{ approvalInfo.reimbursementTypeName }}</span>
                </li>
                <li v-if="approvalInfo.reimbursementType == 'zl'">
                  <div><span>出差地点</span>:</div>
                  <span>{{ approvalInfo.businessPlace }}</span>
                </li>
                <li v-if="approvalInfo.reimbursementType == 'zl'">
                  <div><span></span></div>
                  <span></span>
                </li>
                <li>
                  <div><span>报销用途</span>:</div>
                  <el-tooltip class="item" effect="dark" :content="approvalInfo.reimbursementDesc"
                    placement="top-start">
                    <span class="reimbursementDesc">{{ approvalInfo.reimbursementDesc }}</span>
                  </el-tooltip>
                </li>
              </ul>
              <!-- <img v-if="approvalInfo.isElectronic == 1" class="dz_icon" src="../../assets/dz_icon.png"/> -->
            </div>
            <!-- 出差补助 -->
            <div class="ordinary" v-if="approvalInfo.reimbursementType == 'zl'" style="margin-bottom: 20px">
              <div style="margin-top: 10px">出差详情</div>
            </div>
            <div class="info zlinfo" v-if="approvalInfo.reimbursementType == 'zl'">
              <span v-if="!isBusinessDay" class="spn">出差天数：{{ approvalInfo.businessDay }} 天</span>
              <p v-if="isBusinessDay">
                <span>出差天数：</span>
                <input @input="setBusinessDay(approvalInfo.businessDay)" v-model="approvalInfo.businessDay"
                  type="number" style="
                    width: 50px;
                    border: 0;
                    outline: none;
                    box-shadow: none;
                    padding: 3px 10px;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                  " />
                <span>天</span>
              </p>
              <span v-if="!isBusinessDay" class="spn">费用标准：{{ approvalInfo.businessStandards }}
                {{ getCurrencyUnit(approvalInfo.currency) }} / 天</span>
              <p v-if="isBusinessDay">
                <span>费用标准：</span>
                <input @input="setBusinessStandards(approvalInfo.businessStandards)"
                  v-model="approvalInfo.businessStandards" type="number" style="
                    width: 50px;
                    border: 0;
                    outline: none;
                    box-shadow: none;
                    padding: 3px 10px;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                  " />
                <span>天</span>
              </p>
              <span class="spn">出差补助：<span style="color: red; font-weight: 800">{{ approvalInfo.businessSubsidy
              }}{{ getCurrencyUnit(approvalInfo.currency) }}</span></span>
              <span class="spn" v-if="isShow">
                <span class="setspn" v-if="!isBusinessDay" @click="getBusinessDay">修改</span>
                <span class="setspn onsetspn" v-if="isBusinessDay" @click="noSet">取消</span>
                <span class="setspn" v-if="isBusinessDay" @click="putList"> 完成</span>
              </span>
            </div>
            <!-- 电脑补助 -->
            <div class="ordinary" v-if="computerMoney > 0">
              <div>电脑补助</div>
            </div>
            <div class="cpu-box" v-if="computerMoney > 0">
              <p>
                电脑补助可用额度：<span class="info-right-active">{{ getCurrency(approvalInfo.currency) }} {{
                  computerInfo.balance }}</span>
              </p>
              <p>
                费用标准：<span class="info-right-active">{{ getCurrency(approvalInfo.currency) }} 100.00/月</span>
              </p>
              <p>
                本次电脑补助合计：<span class="info-right-active">{{ getCurrency(approvalInfo.currency) }}{{ computerMoney }}
                </span>
              </p>
            </div>
            <!-- 话费补助 -->
            <div class="ordinary" v-if="showPhone">
              <div>话费补助</div>
            </div>
            <div class="cpu-box" v-if="showPhone">
              <div>
                话费补助额度上限：<span class="info-right-active">{{ getCurrency(approvalInfo.currency)
                }}{{ phoneSubsidy.balance | toThousands }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 自定义表单区域 -->
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <nf-examine-form ref="examineForm" :process="process" @user-select="handleUserSelect"></nf-examine-form>
      </el-card>
      <div style="height: 80px"></div>
      <el-affix position="bottom" :offset="20">
        <el-row class="foot-item" :style="{ width: isCollapse ? 'calc(100% - 71px)' : 'calc(100% - 241px)' }">
          <el-button type="primary" size="default" v-loading="loading" @click="handleSubmit">
            发起
          </el-button>
          <el-button v-if="permission.wf_process_draft" type="success" size="default" v-loading="loading" @click="
            handleDraft({ processDefId: process.id, formKey: process.formKey, variables: form })
            ">
            存为草稿
          </el-button>
        </el-row>
      </el-affix>
    </el-skeleton>

    <!-- 人员选择弹窗 -->
    <nf-user-select ref="user-select" :check-type="checkType" :default-checked="defaultChecked"
      @onConfirm="handleUserSelectConfirm"></nf-user-select>
  </nf-container>
</template>

<script>
import { submit } from '../../../api/demo/leave.js';
import NfExamineForm from '../../../components/nf-exam-form/index.vue';
import NfUserSelect from '../../../components/nf-user-select/index.vue';
import exForm from '../../../mixins/ex-form';
import draft from '../../../mixins/draft';
import dataJson from './data.json';

export default {
  components: {
    NfUserSelect,
    NfExamineForm,
  },
  mixins: [exForm, draft],
  watch: {
    '$route.query.p': {
      handler(val) {
        if (val) {
          const param = JSON.parse(window.atob(val));
          const { processId, processDefKey } = param;
          if (processId || processDefKey) this.getForm(processId, processDefKey);
        }
      },
      immediate: true,
    },
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  data() {
    return {
      defaults: {},
      form: {},
      option: {
        menuBtn: false,
        column: [
          {
            type: 'input',
            label: '报销单名称',
            span: 12,
            display: true,
            prop: 'reimbursementName',
            value: '${this.$store.getters.userInfo.nick_name}',
            readonly: true,
          },
          {
            type: 'input',
            label: '报销人',
            span: 12,
            display: true,
            prop: 'userName',
            value: '${this.$store.getters.userInfo.dept_name}',
            readonly: true,
          },
          {
            type: 'datetimerange',
            label: '请假时间',
            span: 12,
            display: true,
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            prop: 'datetime',
            dataType: 'string',
            required: true,
            rules: [
              {
                required: true,
                message: '开始时间必须填写',
              },
            ],
            change: ({ value }) => {
              if (!value || value.length == 0) {
                this.form.day = undefined;
              } else {
                const d1 = Date.parse(value.split(',')[0]);
                const d2 = Date.parse(value.split(',')[1]);
                const day = (d2 - d1) / (1 * 24 * 60 * 60 * 1000);
                this.form.days = Number(day.toFixed(2));
              }
            },
          },
          {
            type: 'number',
            label: '报销总额',
            span: 12,
            display: true,
            prop: 'reimbursementAmount',
            required: true,
            rules: [
              {
                required: true,
                message: '请假天数必须填写',
              },
            ],
            controls: true,
            controlsPosition: 'right',
            change: ({ value }) => {
              if (value) this.form.reason = '请假' + value + '天';
              else this.form.reason = '';
            },
          },
          {
            type: 'textarea',
            label: '报销用途',
            span: 24,
            display: true,
            prop: 'reimbursementDesc',
            required: true,
            rules: [
              {
                required: true,
                message: '请假理由必须填写',
              },
            ],
          },
          // {
          //   label: '附件',
          //   type: 'upload',
          //   propsHttp: {
          //     res: 'data',
          //     url: 'link',
          //     name: 'originalName',
          //   },
          //   action: '/blade-resource/oss/endpoint/put-file',
          //   display: true,
          //   span: 24,
          //   showFileList: true,
          //   multiple: true,
          //   limit: 10,
          //   prop: 'attachment',
          //   dataType: 'string',
          // },
        ],
        // column: [
        //   {
        //     type: 'input',
        //     label: '创建人',
        //     span: 12,
        //     display: true,
        //     prop: 'creator',
        //     value: '${this.$store.getters.userInfo.nick_name}',
        //     readonly: true,
        //   },
        //   {
        //     type: 'input',
        //     label: '创建部门',
        //     span: 12,
        //     display: true,
        //     prop: 'creatorDept',
        //     value: '${this.$store.getters.userInfo.dept_name}',
        //     readonly: true,
        //   },
        //   {
        //     type: 'datetimerange',
        //     label: '请假时间',
        //     span: 12,
        //     display: true,
        //     format: 'YYYY-MM-DD HH:mm:ss',
        //     valueFormat: 'YYYY-MM-DD HH:mm:ss',
        //     prop: 'datetime',
        //     dataType: 'string',
        //     required: true,
        //     rules: [
        //       {
        //         required: true,
        //         message: '开始时间必须填写',
        //       },
        //     ],
        //     change: ({ value }) => {
        //       if (!value || value.length == 0) {
        //         this.form.day = undefined;
        //       } else {
        //         const d1 = Date.parse(value.split(',')[0]);
        //         const d2 = Date.parse(value.split(',')[1]);
        //         const day = (d2 - d1) / (1 * 24 * 60 * 60 * 1000);
        //         this.form.days = Number(day.toFixed(2));
        //       }
        //     },
        //   },
        //   {
        //     type: 'number',
        //     label: '请假天数',
        //     span: 12,
        //     display: true,
        //     prop: 'days',
        //     required: true,
        //     rules: [
        //       {
        //         required: true,
        //         message: '请假天数必须填写',
        //       },
        //     ],
        //     controls: true,
        //     controlsPosition: 'right',
        //     change: ({ value }) => {
        //       if (value) this.form.reason = '请假' + value + '天';
        //       else this.form.reason = '';
        //     },
        //   },
        //   {
        //     type: 'textarea',
        //     label: '请假理由',
        //     span: 24,
        //     display: true,
        //     prop: 'reason',
        //     required: true,
        //     rules: [
        //       {
        //         required: true,
        //         message: '请假理由必须填写',
        //       },
        //     ],
        //   },
        //   {
        //     label: '附件',
        //     type: 'upload',
        //     propsHttp: {
        //       res: 'data',
        //       url: 'link',
        //       name: 'originalName',
        //     },
        //     action: '/blade-resource/oss/endpoint/put-file',
        //     display: true,
        //     span: 24,
        //     showFileList: true,
        //     multiple: true,
        //     limit: 10,
        //     prop: 'attachment',
        //     dataType: 'string',
        //   },
        // ],
      },
      process: {},
      loading: false,
      // 基本信息
      approvalInfo: {
        newType: 0,
        marketingDirector: '',
        isElectronic: 0,
        userCode: '销售三部-5617',
        employeeNumber: '5617',
        businessPlace: '南京',
        isLinked: 0,
        reimbursementType: 'zl',
        result: 0,
        revocation: 0,
        reimbursementDesc: '南京投标出差',
        businessSubsidy: 120,
        businessStandards: 60,
        client: 1,
        currency: 'CNY',
        reimbursementAmount: 709.38,
        reimbursementTypeName: '差旅',
        reimbursementName: '金小琪-240808-1',
        approvalStage: '常务副总裁',
        marketingDirectorName: '',
        approvalState: 1,
        userName: '金小琪',
        currentAssignee: '周曙',
        userId: 'jinxq',
        createTime: 1723104720000,
        processInstId: '20991329',
        submissionorder: 1,
        businessDay: 2,
        position: '销售助理',
        bid: 331813,
        contracPlace: '1',
      },
      // 报销明细
      tableData: [
        {
          invoiceDetail: [
            {
              photoList: [
                {
                  iid: 1046190,
                  photo_type: '1',
                  invoice_photo_url:
                    'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAKD-sAAZFAdGKLAs310.jpg',
                  aid: 428908,
                },
              ],
              invoiceTypeNew: '3',
              invoiceAmount: 533,
              pid: 65434,
              encryptCode: '',
              type: '',
              resolution: 0,
              sheetsNum: 1,
              subjectId: 2,
              arrivalTime: '2024-08-08',
              invoicer: '',
              invoiceType: '0',
              afterTaxAmount: 488.99,
              happenTime: 1723046400000,
              linkCode: 'anqi',
              pocketLinkFlag: 0,
              revisability: false,
              vehicleCode: 'train',
              invoiceStateList: [
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '草稿',
                  dvid: 1,
                  domainValue: '0',
                  domainOrder: 1,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '待验证',
                  dvid: 2,
                  domainValue: '1',
                  domainOrder: 2,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '验证通过',
                  dvid: 3,
                  domainValue: '2',
                  domainOrder: 3,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '验证拒绝',
                  dvid: 4,
                  domainValue: '3',
                  domainOrder: 4,
                },
              ],
              invoiceCode: '',
              buyer: '',
              checkCode: '',
              consolidated: 0,
              invoiceTypeList: [
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税专用发票',
                  dvid: 11,
                  domainValue: '10100',
                  domainOrder: 1,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税普通发票',
                  dvid: 12,
                  domainValue: '10101',
                  domainOrder: 2,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税电子普通发票',
                  dvid: 13,
                  domainValue: '10102',
                  domainOrder: 3,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税普通发票(卷票)',
                  dvid: 14,
                  domainValue: '10103',
                  domainOrder: 4,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '机动车销售统一发票',
                  dvid: 15,
                  domainValue: '10104',
                  domainOrder: 5,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '二手车销售统一发票',
                  dvid: 16,
                  domainValue: '10105',
                  domainOrder: 6,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '定额发票',
                  dvid: 17,
                  domainValue: '10200',
                  domainOrder: 7,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '机打发票',
                  dvid: 18,
                  domainValue: '10400',
                  domainOrder: 8,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '出租车发票',
                  dvid: 19,
                  domainValue: '10500',
                  domainOrder: 9,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '火车票',
                  dvid: 20,
                  domainValue: '10503',
                  domainOrder: 10,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '客运汽车',
                  dvid: 21,
                  domainValue: '10505',
                  domainOrder: 11,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '航空运输电子客票行程单',
                  dvid: 22,
                  domainValue: '10506',
                  domainOrder: 12,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '过路费发票',
                  dvid: 23,
                  domainValue: '10507',
                  domainOrder: 13,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '可报销其他发票',
                  dvid: 24,
                  domainValue: '10900',
                  domainOrder: 14,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '其他',
                  dvid: 25,
                  domainValue: '00000',
                  domainOrder: 15,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '国际小票',
                  dvid: 26,
                  domainValue: '20100',
                  domainOrder: 16,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '飞机票',
                  dvid: 27,
                  domainValue: '10508',
                  domainOrder: 17,
                },
              ],
              month: '',
              cardId: '',
              goOff: '2024-08-08',
              pocketName: '南京出差',
              taxAmount: 44.01,
              departurePlace: '南京',
              iid: 1046190,
              feename: '',
              invoiceState: 1,
              buyerNumber: '',
              vehicle: '火车',
              arrivalPlace: '北京',
              certificateDtoList: [],
              ePhotoList: [],
              invoiceNumber: '',
              currency: 'CNY',
              reimbursementName: '金小琪-240808-1',
              subjectName: '差旅交通费',
              invoicePurpose: '南京回北京',
              invoiceLinkList: [
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联客户',
                  dvid: 30,
                  domainValue: 'link_customer',
                  domainOrder: 1,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联销售机会',
                  dvid: 31,
                  domainValue: 'link_opportunity',
                  domainOrder: 2,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联投标',
                  dvid: 32,
                  domainValue: 'link_founds',
                  domainOrder: 3,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联项目',
                  dvid: 33,
                  domainValue: 'link_project',
                  domainOrder: 4,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联日志',
                  dvid: 34,
                  domainValue: 'link_daily',
                  domainOrder: 5,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联合同',
                  dvid: 35,
                  domainValue: 'link_contract',
                  domainOrder: 6,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联人员',
                  dvid: 36,
                  domainValue: 'link_staff',
                  domainOrder: 7,
                },
              ],
              invoiceDate: '2024-08-08',
              invoiceLinkState: 1,
              userName: '金小琪',
              userId: 'jinxq',
              invoiceSpecial: 0,
              createTime: '2024-08-08 14:16',
              linkType: 'link_staff',
              invoiceLinkInfo: {
                linkTypeName: '关联人员',
                linkCode: 'anqi',
                linkId: 1046179,
                linkIid: 1046190,
                linkType: 'link_staff',
                linkDate: '',
                linkName: '安琪',
                linkRemarks: '',
              },
            },
          ],
          reimbursement_amount: 533,
          user_name: '金小琪',
          actual_amount: 533,
          sign: 1001,
          initial_amount: 533,
          reimbursement_date: '2024-08-08',
          sid: 2,
          voiceList: [1046190],
          voiceCount: 1,
          create_date: '2024-08-08 16:11:30',
          reimbursement_purpose: '南京回北京',
          link_type_id: 1046190,
          revisability: false,
          computerFlag: '0',
          rbmDate: '',
          business_subsidy: 120,
          link_type: '0',
          bdid: 1123687,
          consolidated: 0,
          month: '',
          user_id: 'jinxq',
          business_standards: 60,
          subject_name: '差旅交通费',
          invoceAggregation: {
            sumdaily: 0,
            sumfounds: 0,
            sumcustomer: 0,
            sumcontract: 0,
            sumopportunity: 0,
            sumproject: 0,
            sumstaff: 0,
          },
          bid: 331813,
        },
        {
          invoiceDetail: [
            {
              photoList: [
                {
                  iid: 1046192,
                  photo_type: '1',
                  invoice_photo_url:
                    'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aFGATZVIAAE1CgalNIc707.png',
                  aid: 428941,
                },
              ],
              invoiceTypeNew: '1',
              invoiceAmount: 16.44,
              pid: 65434,
              encryptCode: '',
              type: '',
              resolution: 0,
              sheetsNum: 1,
              subjectId: 20,
              invoicer: '',
              invoiceType: '0',
              afterTaxAmount: 0,
              happenTime: 1723046400000,
              linkCode: 'anqi',
              pocketLinkFlag: 0,
              revisability: false,
              vehicleCode: 'other',
              invoiceStateList: [
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '草稿',
                  dvid: 1,
                  domainValue: '0',
                  domainOrder: 1,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '待验证',
                  dvid: 2,
                  domainValue: '1',
                  domainOrder: 2,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '验证通过',
                  dvid: 3,
                  domainValue: '2',
                  domainOrder: 3,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '验证拒绝',
                  dvid: 4,
                  domainValue: '3',
                  domainOrder: 4,
                },
              ],
              invoiceCode: '',
              buyer: '',
              checkCode: '',
              consolidated: 0,
              invoiceTypeList: [
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税专用发票',
                  dvid: 11,
                  domainValue: '10100',
                  domainOrder: 1,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税普通发票',
                  dvid: 12,
                  domainValue: '10101',
                  domainOrder: 2,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税电子普通发票',
                  dvid: 13,
                  domainValue: '10102',
                  domainOrder: 3,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税普通发票(卷票)',
                  dvid: 14,
                  domainValue: '10103',
                  domainOrder: 4,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '机动车销售统一发票',
                  dvid: 15,
                  domainValue: '10104',
                  domainOrder: 5,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '二手车销售统一发票',
                  dvid: 16,
                  domainValue: '10105',
                  domainOrder: 6,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '定额发票',
                  dvid: 17,
                  domainValue: '10200',
                  domainOrder: 7,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '机打发票',
                  dvid: 18,
                  domainValue: '10400',
                  domainOrder: 8,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '出租车发票',
                  dvid: 19,
                  domainValue: '10500',
                  domainOrder: 9,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '火车票',
                  dvid: 20,
                  domainValue: '10503',
                  domainOrder: 10,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '客运汽车',
                  dvid: 21,
                  domainValue: '10505',
                  domainOrder: 11,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '航空运输电子客票行程单',
                  dvid: 22,
                  domainValue: '10506',
                  domainOrder: 12,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '过路费发票',
                  dvid: 23,
                  domainValue: '10507',
                  domainOrder: 13,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '可报销其他发票',
                  dvid: 24,
                  domainValue: '10900',
                  domainOrder: 14,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '其他',
                  dvid: 25,
                  domainValue: '00000',
                  domainOrder: 15,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '国际小票',
                  dvid: 26,
                  domainValue: '20100',
                  domainOrder: 16,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '飞机票',
                  dvid: 27,
                  domainValue: '10508',
                  domainOrder: 17,
                },
              ],
              month: '',
              cardId: '',
              pocketName: '南京出差',
              taxAmount: 0,
              departurePlace: '',
              iid: 1046192,
              feename: '',
              invoiceState: 1,
              buyerNumber: '',
              vehicle: '其他（地铁客车出租车等）',
              arrivalPlace: '',
              certificateDtoList: [],
              ePhotoList: [
                {
                  iid: 1046192,
                  photo_type: '4',
                  name: '电子发票附件',
                  invoice_photo_url:
                    'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmATSEmAAGjO0Y0HWE662.pdf',
                  aid: 428910,
                },
              ],
              invoiceNumber: '24317000000631780967',
              currency: 'CNY',
              reimbursementName: '金小琪-240808-1',
              subjectName: '市内交通费',
              invoicePurpose: '客户地回酒店',
              invoiceLinkList: [
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联客户',
                  dvid: 30,
                  domainValue: 'link_customer',
                  domainOrder: 1,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联销售机会',
                  dvid: 31,
                  domainValue: 'link_opportunity',
                  domainOrder: 2,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联投标',
                  dvid: 32,
                  domainValue: 'link_founds',
                  domainOrder: 3,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联项目',
                  dvid: 33,
                  domainValue: 'link_project',
                  domainOrder: 4,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联日志',
                  dvid: 34,
                  domainValue: 'link_daily',
                  domainOrder: 5,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联合同',
                  dvid: 35,
                  domainValue: 'link_contract',
                  domainOrder: 6,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联人员',
                  dvid: 36,
                  domainValue: 'link_staff',
                  domainOrder: 7,
                },
              ],
              invoiceDate: '2024-08-08 14:09',
              invoiceLinkState: 1,
              userName: '金小琪',
              userId: 'jinxq',
              invoiceSpecial: 0,
              createTime: '2024-08-08 14:16',
              linkType: 'link_staff',
              invoiceLinkInfo: {
                linkTypeName: '关联人员',
                linkCode: 'anqi',
                linkId: 1046181,
                linkIid: 1046192,
                linkType: 'link_staff',
                linkDate: '',
                linkName: '安琪',
                linkRemarks: '',
              },
            },
          ],
          reimbursement_amount: 16.44,
          user_name: '金小琪',
          actual_amount: 16.44,
          sign: 1002,
          initial_amount: 16.44,
          reimbursement_date: '2024-08-08',
          sid: 20,
          voiceList: [1046192],
          voiceCount: 1,
          create_date: '2024-08-08 16:11:30',
          reimbursement_purpose: '客户地回酒店',
          link_type_id: 1046192,
          revisability: false,
          computerFlag: '0',
          rbmDate: '',
          business_subsidy: 120,
          link_type: '0',
          bdid: 1123688,
          consolidated: 0,
          month: '',
          user_id: 'jinxq',
          business_standards: 60,
          subject_name: '市内交通费',
          invoceAggregation: {
            sumdaily: 0,
            sumfounds: 0,
            sumcustomer: 0,
            sumcontract: 0,
            sumopportunity: 0,
            sumproject: 0,
            sumstaff: 0,
          },
          bid: 331813,
        },
        {
          invoiceDetail: [
            {
              photoList: [
                {
                  iid: 1046238,
                  photo_type: '1',
                  invoice_photo_url:
                    'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAEMQ7AAEgbrzc8lw689.png',
                  aid: 428964,
                },
              ],
              invoiceTypeNew: '1',
              invoiceAmount: 15.32,
              pid: 65434,
              encryptCode: '',
              type: '',
              resolution: 0,
              sheetsNum: 1,
              subjectId: 20,
              invoicer: '',
              invoiceType: '0',
              afterTaxAmount: 0,
              happenTime: 1723046400000,
              linkCode: 'anqi',
              pocketLinkFlag: 0,
              revisability: false,
              vehicleCode: 'other',
              invoiceStateList: [
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '草稿',
                  dvid: 1,
                  domainValue: '0',
                  domainOrder: 1,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '待验证',
                  dvid: 2,
                  domainValue: '1',
                  domainOrder: 2,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '验证通过',
                  dvid: 3,
                  domainValue: '2',
                  domainOrder: 3,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '验证拒绝',
                  dvid: 4,
                  domainValue: '3',
                  domainOrder: 4,
                },
              ],
              invoiceCode: '011002400111',
              buyer: '',
              checkCode: '',
              consolidated: 0,
              invoiceTypeList: [
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税专用发票',
                  dvid: 11,
                  domainValue: '10100',
                  domainOrder: 1,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税普通发票',
                  dvid: 12,
                  domainValue: '10101',
                  domainOrder: 2,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税电子普通发票',
                  dvid: 13,
                  domainValue: '10102',
                  domainOrder: 3,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税普通发票(卷票)',
                  dvid: 14,
                  domainValue: '10103',
                  domainOrder: 4,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '机动车销售统一发票',
                  dvid: 15,
                  domainValue: '10104',
                  domainOrder: 5,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '二手车销售统一发票',
                  dvid: 16,
                  domainValue: '10105',
                  domainOrder: 6,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '定额发票',
                  dvid: 17,
                  domainValue: '10200',
                  domainOrder: 7,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '机打发票',
                  dvid: 18,
                  domainValue: '10400',
                  domainOrder: 8,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '出租车发票',
                  dvid: 19,
                  domainValue: '10500',
                  domainOrder: 9,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '火车票',
                  dvid: 20,
                  domainValue: '10503',
                  domainOrder: 10,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '客运汽车',
                  dvid: 21,
                  domainValue: '10505',
                  domainOrder: 11,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '航空运输电子客票行程单',
                  dvid: 22,
                  domainValue: '10506',
                  domainOrder: 12,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '过路费发票',
                  dvid: 23,
                  domainValue: '10507',
                  domainOrder: 13,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '可报销其他发票',
                  dvid: 24,
                  domainValue: '10900',
                  domainOrder: 14,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '其他',
                  dvid: 25,
                  domainValue: '00000',
                  domainOrder: 15,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '国际小票',
                  dvid: 26,
                  domainValue: '20100',
                  domainOrder: 16,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '飞机票',
                  dvid: 27,
                  domainValue: '10508',
                  domainOrder: 17,
                },
              ],
              month: '',
              cardId: '',
              pocketName: '南京出差',
              taxAmount: 0,
              departurePlace: '',
              iid: 1046238,
              feename: '',
              invoiceState: 1,
              buyerNumber: '',
              vehicle: '其他（地铁客车出租车等）',
              arrivalPlace: '',
              certificateDtoList: [],
              ePhotoList: [
                {
                  iid: 1046238,
                  photo_type: '4',
                  name: '电子发票附件',
                  invoice_photo_url:
                    'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAE5_7AACtM-Mj8Sc723.pdf',
                  aid: 428965,
                },
              ],
              invoiceNumber: '46118101',
              currency: 'CNY',
              reimbursementName: '金小琪-240808-1',
              subjectName: '市内交通费',
              invoicePurpose: '客户地回公司',
              invoiceLinkList: [
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联客户',
                  dvid: 30,
                  domainValue: 'link_customer',
                  domainOrder: 1,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联销售机会',
                  dvid: 31,
                  domainValue: 'link_opportunity',
                  domainOrder: 2,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联投标',
                  dvid: 32,
                  domainValue: 'link_founds',
                  domainOrder: 3,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联项目',
                  dvid: 33,
                  domainValue: 'link_project',
                  domainOrder: 4,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联日志',
                  dvid: 34,
                  domainValue: 'link_daily',
                  domainOrder: 5,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联合同',
                  dvid: 35,
                  domainValue: 'link_contract',
                  domainOrder: 6,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联人员',
                  dvid: 36,
                  domainValue: 'link_staff',
                  domainOrder: 7,
                },
              ],
              invoiceDate: '2024-08-08 14:44',
              invoiceLinkState: 1,
              userName: '金小琪',
              userId: 'jinxq',
              invoiceSpecial: 0,
              createTime: '2024-08-08 14:45',
              linkType: 'link_staff',
              invoiceLinkInfo: {
                linkTypeName: '关联人员',
                linkCode: 'anqi',
                linkId: 1046227,
                linkIid: 1046238,
                linkType: 'link_staff',
                linkDate: '',
                linkName: '安琪',
                linkRemarks: '',
              },
            },
          ],
          reimbursement_amount: 15.32,
          user_name: '金小琪',
          actual_amount: 15.32,
          sign: 1003,
          initial_amount: 15.32,
          reimbursement_date: '2024-08-08',
          sid: 20,
          voiceList: [1046238],
          voiceCount: 1,
          create_date: '2024-08-08 16:11:30',
          reimbursement_purpose: '客户地回公司',
          link_type_id: 1046238,
          revisability: false,
          computerFlag: '0',
          rbmDate: '',
          business_subsidy: 120,
          link_type: '0',
          bdid: 1123686,
          consolidated: 0,
          month: '',
          user_id: 'jinxq',
          business_standards: 60,
          subject_name: '市内交通费',
          invoceAggregation: {
            sumdaily: 0,
            sumfounds: 0,
            sumcustomer: 0,
            sumcontract: 0,
            sumopportunity: 0,
            sumproject: 0,
            sumstaff: 0,
          },
          bid: 331813,
        },
        {
          invoiceDetail: [
            {
              photoList: [
                {
                  iid: 1046337,
                  photo_type: '1',
                  invoice_photo_url:
                    'http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeAEdnSAADUoGhdlXA721.jpg',
                  aid: 429052,
                },
              ],
              invoiceTypeNew: '1',
              invoiceAmount: 24.62,
              pid: 65434,
              encryptCode: '',
              type: '',
              resolution: 0,
              sheetsNum: 1,
              subjectId: 20,
              invoicer: '',
              invoiceType: '0',
              afterTaxAmount: 0,
              happenTime: 1723046400000,
              linkCode: 'anqi',
              pocketLinkFlag: 0,
              revisability: false,
              vehicleCode: 'other',
              invoiceStateList: [
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '草稿',
                  dvid: 1,
                  domainValue: '0',
                  domainOrder: 1,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '待验证',
                  dvid: 2,
                  domainValue: '1',
                  domainOrder: 2,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '验证通过',
                  dvid: 3,
                  domainValue: '2',
                  domainOrder: 3,
                },
                {
                  domainCode: 'invoiceState',
                  domainValueDesc: '验证拒绝',
                  dvid: 4,
                  domainValue: '3',
                  domainOrder: 4,
                },
              ],
              invoiceCode: '011002400111',
              buyer: '',
              checkCode: '',
              consolidated: 0,
              invoiceTypeList: [
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税专用发票',
                  dvid: 11,
                  domainValue: '10100',
                  domainOrder: 1,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税普通发票',
                  dvid: 12,
                  domainValue: '10101',
                  domainOrder: 2,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税电子普通发票',
                  dvid: 13,
                  domainValue: '10102',
                  domainOrder: 3,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '增值税普通发票(卷票)',
                  dvid: 14,
                  domainValue: '10103',
                  domainOrder: 4,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '机动车销售统一发票',
                  dvid: 15,
                  domainValue: '10104',
                  domainOrder: 5,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '二手车销售统一发票',
                  dvid: 16,
                  domainValue: '10105',
                  domainOrder: 6,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '定额发票',
                  dvid: 17,
                  domainValue: '10200',
                  domainOrder: 7,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '机打发票',
                  dvid: 18,
                  domainValue: '10400',
                  domainOrder: 8,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '出租车发票',
                  dvid: 19,
                  domainValue: '10500',
                  domainOrder: 9,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '火车票',
                  dvid: 20,
                  domainValue: '10503',
                  domainOrder: 10,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '客运汽车',
                  dvid: 21,
                  domainValue: '10505',
                  domainOrder: 11,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '航空运输电子客票行程单',
                  dvid: 22,
                  domainValue: '10506',
                  domainOrder: 12,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '过路费发票',
                  dvid: 23,
                  domainValue: '10507',
                  domainOrder: 13,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '可报销其他发票',
                  dvid: 24,
                  domainValue: '10900',
                  domainOrder: 14,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '其他',
                  dvid: 25,
                  domainValue: '00000',
                  domainOrder: 15,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '国际小票',
                  dvid: 26,
                  domainValue: '20100',
                  domainOrder: 16,
                },
                {
                  domainCode: 'invoiceType',
                  domainValueDesc: '飞机票',
                  dvid: 27,
                  domainValue: '10508',
                  domainOrder: 17,
                },
              ],
              month: '',
              cardId: '',
              pocketName: '南京出差',
              taxAmount: 0,
              departurePlace: '',
              iid: 1046337,
              feename: '',
              invoiceState: 1,
              buyerNumber: '',
              vehicle: '其他（地铁客车出租车等）',
              arrivalPlace: '',
              certificateDtoList: [],
              ePhotoList: [
                {
                  iid: 1046337,
                  photo_type: '4',
                  name: '电子发票附件',
                  invoice_photo_url:
                    'http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeARxF6AACTh2k4FAE469.pdf',
                  aid: 429053,
                },
              ],
              invoiceNumber: '40706826',
              currency: 'CNY',
              reimbursementName: '金小琪-240808-1',
              subjectName: '市内交通费',
              invoicePurpose: '酒店到南京车站',
              invoiceLinkList: [
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联客户',
                  dvid: 30,
                  domainValue: 'link_customer',
                  domainOrder: 1,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联销售机会',
                  dvid: 31,
                  domainValue: 'link_opportunity',
                  domainOrder: 2,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联投标',
                  dvid: 32,
                  domainValue: 'link_founds',
                  domainOrder: 3,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联项目',
                  dvid: 33,
                  domainValue: 'link_project',
                  domainOrder: 4,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联日志',
                  dvid: 34,
                  domainValue: 'link_daily',
                  domainOrder: 5,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联合同',
                  dvid: 35,
                  domainValue: 'link_contract',
                  domainOrder: 6,
                },
                {
                  domainCode: 'linkType',
                  domainValueDesc: '关联人员',
                  dvid: 36,
                  domainValue: 'link_staff',
                  domainOrder: 7,
                },
              ],
              invoiceDate: '2024-08-08 15:37',
              invoiceLinkState: 1,
              userName: '金小琪',
              userId: 'jinxq',
              invoiceSpecial: 0,
              createTime: '2024-08-08 15:38',
              linkType: 'link_staff',
              invoiceLinkInfo: {
                linkTypeName: '关联人员',
                linkCode: 'anqi',
                linkId: 1046326,
                linkIid: 1046337,
                linkType: 'link_staff',
                linkDate: '',
                linkName: '安琪',
                linkRemarks: '',
              },
            },
          ],
          reimbursement_amount: 24.62,
          user_name: '金小琪',
          actual_amount: 24.62,
          sign: 1004,
          initial_amount: 24.62,
          reimbursement_date: '2024-08-08',
          sid: 20,
          voiceList: [1046337],
          voiceCount: 1,
          create_date: '2024-08-08 16:11:30',
          reimbursement_purpose: '酒店到南京车站',
          link_type_id: 1046337,
          revisability: false,
          computerFlag: '0',
          rbmDate: '',
          business_subsidy: 120,
          link_type: '0',
          bdid: 1123685,
          consolidated: 0,
          month: '',
          user_id: 'jinxq',
          business_standards: 60,
          subject_name: '市内交通费',
          invoceAggregation: {
            sumdaily: 0,
            sumfounds: 0,
            sumcustomer: 0,
            sumcontract: 0,
            sumopportunity: 0,
            sumproject: 0,
            sumstaff: 0,
          },
          bid: 331813,
        },
      ],
      allMinData: [],
      travelList: [],
      hotelList: [],
      plainList: [],
      // 部门
      department: '北京总部-销售三部-金小琪-5617', //需要单独调接口
      isBusinessDay: false,
      computerMoney: 0,
      // 税额、未税总额
      plainTaxSum: 0,
      plainAfterTaxSum: 0,

      travelTaxSum: 0,
      travelAfterTaxSum: 0,

      hotelTaxSum: 0,
      hotelAfterTaxSum: 0,
      // 项目日志
      resultList: [], //需要单独调接口
      // 工作日志
      LogList: [],
      // 电脑补
      computerInfo: {
        balance: 100,
        accumulative: 100,
        state: 1,
        userid: 'jinxq',
        cid: 6719,
      }, //需要单独调接口
      showPhone: false, // 是否显示话费可用额度区块
      phoneSubsidy: {
        // 话费补-可用额度相关
        useAmount: 0, // 本次使用金额
        balance: 200, // 可用额度    //这个值需要单独调接口
      },
      // 报销月份
      months: [
        {
          label: '2024-12',
          value: '2024-12',
        },
        {
          label: '2024-11',
          value: '2024-11',
        },
        {
          label: '2024-10',
          value: '2024-10',
        },
      ], //需要单独调接口
      // 审批日志
      journalData: [
        {
          userId: '安琪',
          time: 1723124328000,
          fullMessage: '同意。',
        },
        {
          userId: '王健',
          time: 1723524676000,
          fullMessage: '同意。',
        },
        {
          userId: '王佺',
          time: 1724382202000,
          fullMessage: '同意。',
        },
        {
          userId: '王佺',
          time: 1724382202000,
          fullMessage: '同意(其他审批环节已同意,系统默认通过)',
        },
      ], //需要单独调接口
      // 币种
      currencylist: [
        {
          unit: '元',
          code: '¥',
          currency: 'CNY',
          disabled: false,
          label: '人民币',
        },
        {
          unit: '港元',
          code: 'HK$',
          currency: 'HKD',
          disabled: false,
          label: '港币',
        },
        {
          unit: '美元',
          code: '$',
          currency: 'USD',
          disabled: true,
          label: '美元',
        },
        {
          unit: '新加坡元',
          code: 'S$',
          currency: 'SGD',
          disabled: true,
          label: '新加坡元',
        },
        {
          unit: '英镑',
          code: '￡',
          currency: 'GBP',
          disabled: true,
          label: '英镑',
        },
        {
          unit: '雷亚尔',
          code: 'R$',
          currency: 'BRL',
          disabled: true,
          label: '雷亚尔',
        },
      ], //需要单独调接口
      // 财务详情接口返回数据
      result: {
        ePdf: [
          'http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeAEdnSAADUoGhdlXA721.jpg',
          'http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeARxF6AACTh2k4FAE469.pdf',
          'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAEMQ7AAEgbrzc8lw689.png',
          'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAE5_7AACtM-Mj8Sc723.pdf',
          'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmATSEmAAGjO0Y0HWE662.pdf',
          'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aFGATZVIAAE1CgalNIc707.png',
        ],
        data: [
          {
            invoiceDetail: [
              {
                photoList: [
                  {
                    iid: 1046190,
                    photo_type: '1',
                    invoice_photo_url:
                      'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAKD-sAAZFAdGKLAs310.jpg',
                    aid: 428908,
                  },
                ],
                invoiceTypeNew: '3',
                invoiceAmount: 533,
                pid: 65434,
                encryptCode: '',
                type: '',
                resolution: 0,
                sheetsNum: 1,
                subjectId: 2,
                arrivalTime: '2024-08-08',
                invoicer: '',
                invoiceType: '0',
                afterTaxAmount: 488.99,
                happenTime: 1723046400000,
                linkCode: 'anqi',
                pocketLinkFlag: 0,
                revisability: false,
                vehicleCode: 'train',
                invoiceStateList: [
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '草稿',
                    dvid: 1,
                    domainValue: '0',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '待验证',
                    dvid: 2,
                    domainValue: '1',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '验证通过',
                    dvid: 3,
                    domainValue: '2',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '验证拒绝',
                    dvid: 4,
                    domainValue: '3',
                    domainOrder: 4,
                  },
                ],
                invoiceCode: '',
                buyer: '',
                checkCode: '',
                consolidated: 0,
                invoiceTypeList: [
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税专用发票',
                    dvid: 11,
                    domainValue: '10100',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税普通发票',
                    dvid: 12,
                    domainValue: '10101',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税电子普通发票',
                    dvid: 13,
                    domainValue: '10102',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税普通发票(卷票)',
                    dvid: 14,
                    domainValue: '10103',
                    domainOrder: 4,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '机动车销售统一发票',
                    dvid: 15,
                    domainValue: '10104',
                    domainOrder: 5,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '二手车销售统一发票',
                    dvid: 16,
                    domainValue: '10105',
                    domainOrder: 6,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '定额发票',
                    dvid: 17,
                    domainValue: '10200',
                    domainOrder: 7,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '机打发票',
                    dvid: 18,
                    domainValue: '10400',
                    domainOrder: 8,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '出租车发票',
                    dvid: 19,
                    domainValue: '10500',
                    domainOrder: 9,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '火车票',
                    dvid: 20,
                    domainValue: '10503',
                    domainOrder: 10,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '客运汽车',
                    dvid: 21,
                    domainValue: '10505',
                    domainOrder: 11,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '航空运输电子客票行程单',
                    dvid: 22,
                    domainValue: '10506',
                    domainOrder: 12,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '过路费发票',
                    dvid: 23,
                    domainValue: '10507',
                    domainOrder: 13,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '可报销其他发票',
                    dvid: 24,
                    domainValue: '10900',
                    domainOrder: 14,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '其他',
                    dvid: 25,
                    domainValue: '00000',
                    domainOrder: 15,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '国际小票',
                    dvid: 26,
                    domainValue: '20100',
                    domainOrder: 16,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '飞机票',
                    dvid: 27,
                    domainValue: '10508',
                    domainOrder: 17,
                  },
                ],
                month: '',
                cardId: '',
                goOff: '2024-08-08',
                pocketName: '南京出差',
                taxAmount: 44.01,
                departurePlace: '南京',
                iid: 1046190,
                feename: '',
                invoiceState: 1,
                buyerNumber: '',
                vehicle: '火车',
                arrivalPlace: '北京',
                certificateDtoList: [],
                ePhotoList: [],
                invoiceNumber: '',
                currency: 'CNY',
                reimbursementName: '金小琪-240808-1',
                subjectName: '差旅交通费',
                invoicePurpose: '南京回北京',
                invoiceLinkList: [
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联客户',
                    dvid: 30,
                    domainValue: 'link_customer',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联销售机会',
                    dvid: 31,
                    domainValue: 'link_opportunity',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联投标',
                    dvid: 32,
                    domainValue: 'link_founds',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联项目',
                    dvid: 33,
                    domainValue: 'link_project',
                    domainOrder: 4,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联日志',
                    dvid: 34,
                    domainValue: 'link_daily',
                    domainOrder: 5,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联合同',
                    dvid: 35,
                    domainValue: 'link_contract',
                    domainOrder: 6,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联人员',
                    dvid: 36,
                    domainValue: 'link_staff',
                    domainOrder: 7,
                  },
                ],
                invoiceDate: '2024-08-08',
                invoiceLinkState: 1,
                userName: '金小琪',
                userId: 'jinxq',
                invoiceSpecial: 0,
                createTime: '2024-08-08 14:16',
                linkType: 'link_staff',
                invoiceLinkInfo: {
                  linkTypeName: '关联人员',
                  linkCode: 'anqi',
                  linkId: 1046179,
                  linkIid: 1046190,
                  linkType: 'link_staff',
                  linkDate: '',
                  linkName: '安琪',
                  linkRemarks: '',
                },
              },
            ],
            reimbursement_amount: 533,
            user_name: '金小琪',
            actual_amount: 533,
            sign: 1001,
            initial_amount: 533,
            reimbursement_date: '2024-08-08',
            sid: 2,
            voiceList: [1046190],
            voiceCount: 1,
            create_date: '2024-08-08 16:11:30',
            reimbursement_purpose: '南京回北京',
            link_type_id: 1046190,
            revisability: false,
            computerFlag: '0',
            rbmDate: '',
            business_subsidy: 120,
            link_type: '0',
            bdid: 1123687,
            consolidated: 0,
            month: '',
            user_id: 'jinxq',
            business_standards: 60,
            subject_name: '差旅交通费',
            invoceAggregation: {
              sumdaily: 0,
              sumfounds: 0,
              sumcustomer: 0,
              sumcontract: 0,
              sumopportunity: 0,
              sumproject: 0,
              sumstaff: 0,
            },
            bid: 331813,
          },
          {
            invoiceDetail: [
              {
                photoList: [
                  {
                    iid: 1046192,
                    photo_type: '1',
                    invoice_photo_url:
                      'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aFGATZVIAAE1CgalNIc707.png',
                    aid: 428941,
                  },
                ],
                invoiceTypeNew: '1',
                invoiceAmount: 16.44,
                pid: 65434,
                encryptCode: '',
                type: '',
                resolution: 0,
                sheetsNum: 1,
                subjectId: 20,
                invoicer: '',
                invoiceType: '0',
                afterTaxAmount: 0,
                happenTime: 1723046400000,
                linkCode: 'anqi',
                pocketLinkFlag: 0,
                revisability: false,
                vehicleCode: 'other',
                invoiceStateList: [
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '草稿',
                    dvid: 1,
                    domainValue: '0',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '待验证',
                    dvid: 2,
                    domainValue: '1',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '验证通过',
                    dvid: 3,
                    domainValue: '2',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '验证拒绝',
                    dvid: 4,
                    domainValue: '3',
                    domainOrder: 4,
                  },
                ],
                invoiceCode: '',
                buyer: '',
                checkCode: '',
                consolidated: 0,
                invoiceTypeList: [
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税专用发票',
                    dvid: 11,
                    domainValue: '10100',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税普通发票',
                    dvid: 12,
                    domainValue: '10101',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税电子普通发票',
                    dvid: 13,
                    domainValue: '10102',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税普通发票(卷票)',
                    dvid: 14,
                    domainValue: '10103',
                    domainOrder: 4,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '机动车销售统一发票',
                    dvid: 15,
                    domainValue: '10104',
                    domainOrder: 5,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '二手车销售统一发票',
                    dvid: 16,
                    domainValue: '10105',
                    domainOrder: 6,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '定额发票',
                    dvid: 17,
                    domainValue: '10200',
                    domainOrder: 7,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '机打发票',
                    dvid: 18,
                    domainValue: '10400',
                    domainOrder: 8,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '出租车发票',
                    dvid: 19,
                    domainValue: '10500',
                    domainOrder: 9,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '火车票',
                    dvid: 20,
                    domainValue: '10503',
                    domainOrder: 10,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '客运汽车',
                    dvid: 21,
                    domainValue: '10505',
                    domainOrder: 11,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '航空运输电子客票行程单',
                    dvid: 22,
                    domainValue: '10506',
                    domainOrder: 12,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '过路费发票',
                    dvid: 23,
                    domainValue: '10507',
                    domainOrder: 13,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '可报销其他发票',
                    dvid: 24,
                    domainValue: '10900',
                    domainOrder: 14,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '其他',
                    dvid: 25,
                    domainValue: '00000',
                    domainOrder: 15,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '国际小票',
                    dvid: 26,
                    domainValue: '20100',
                    domainOrder: 16,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '飞机票',
                    dvid: 27,
                    domainValue: '10508',
                    domainOrder: 17,
                  },
                ],
                month: '',
                cardId: '',
                pocketName: '南京出差',
                taxAmount: 0,
                departurePlace: '',
                iid: 1046192,
                feename: '',
                invoiceState: 1,
                buyerNumber: '',
                vehicle: '其他（地铁客车出租车等）',
                arrivalPlace: '',
                certificateDtoList: [],
                ePhotoList: [
                  {
                    iid: 1046192,
                    photo_type: '4',
                    name: '电子发票附件',
                    invoice_photo_url:
                      'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmATSEmAAGjO0Y0HWE662.pdf',
                    aid: 428910,
                  },
                ],
                invoiceNumber: '24317000000631780967',
                currency: 'CNY',
                reimbursementName: '金小琪-240808-1',
                subjectName: '市内交通费',
                invoicePurpose: '客户地回酒店',
                invoiceLinkList: [
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联客户',
                    dvid: 30,
                    domainValue: 'link_customer',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联销售机会',
                    dvid: 31,
                    domainValue: 'link_opportunity',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联投标',
                    dvid: 32,
                    domainValue: 'link_founds',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联项目',
                    dvid: 33,
                    domainValue: 'link_project',
                    domainOrder: 4,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联日志',
                    dvid: 34,
                    domainValue: 'link_daily',
                    domainOrder: 5,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联合同',
                    dvid: 35,
                    domainValue: 'link_contract',
                    domainOrder: 6,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联人员',
                    dvid: 36,
                    domainValue: 'link_staff',
                    domainOrder: 7,
                  },
                ],
                invoiceDate: '2024-08-08 14:09',
                invoiceLinkState: 1,
                userName: '金小琪',
                userId: 'jinxq',
                invoiceSpecial: 0,
                createTime: '2024-08-08 14:16',
                linkType: 'link_staff',
                invoiceLinkInfo: {
                  linkTypeName: '关联人员',
                  linkCode: 'anqi',
                  linkId: 1046181,
                  linkIid: 1046192,
                  linkType: 'link_staff',
                  linkDate: '',
                  linkName: '安琪',
                  linkRemarks: '',
                },
              },
            ],
            reimbursement_amount: 16.44,
            user_name: '金小琪',
            actual_amount: 16.44,
            sign: 1002,
            initial_amount: 16.44,
            reimbursement_date: '2024-08-08',
            sid: 20,
            voiceList: [1046192],
            voiceCount: 1,
            create_date: '2024-08-08 16:11:30',
            reimbursement_purpose: '客户地回酒店',
            link_type_id: 1046192,
            revisability: false,
            computerFlag: '0',
            rbmDate: '',
            business_subsidy: 120,
            link_type: '0',
            bdid: 1123688,
            consolidated: 0,
            month: '',
            user_id: 'jinxq',
            business_standards: 60,
            subject_name: '市内交通费',
            invoceAggregation: {
              sumdaily: 0,
              sumfounds: 0,
              sumcustomer: 0,
              sumcontract: 0,
              sumopportunity: 0,
              sumproject: 0,
              sumstaff: 0,
            },
            bid: 331813,
          },
          {
            invoiceDetail: [
              {
                photoList: [
                  {
                    iid: 1046238,
                    photo_type: '1',
                    invoice_photo_url:
                      'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAEMQ7AAEgbrzc8lw689.png',
                    aid: 428964,
                  },
                ],
                invoiceTypeNew: '1',
                invoiceAmount: 15.32,
                pid: 65434,
                encryptCode: '',
                type: '',
                resolution: 0,
                sheetsNum: 1,
                subjectId: 20,
                invoicer: '',
                invoiceType: '0',
                afterTaxAmount: 0,
                happenTime: 1723046400000,
                linkCode: 'anqi',
                pocketLinkFlag: 0,
                revisability: false,
                vehicleCode: 'other',
                invoiceStateList: [
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '草稿',
                    dvid: 1,
                    domainValue: '0',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '待验证',
                    dvid: 2,
                    domainValue: '1',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '验证通过',
                    dvid: 3,
                    domainValue: '2',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '验证拒绝',
                    dvid: 4,
                    domainValue: '3',
                    domainOrder: 4,
                  },
                ],
                invoiceCode: '011002400111',
                buyer: '',
                checkCode: '',
                consolidated: 0,
                invoiceTypeList: [
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税专用发票',
                    dvid: 11,
                    domainValue: '10100',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税普通发票',
                    dvid: 12,
                    domainValue: '10101',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税电子普通发票',
                    dvid: 13,
                    domainValue: '10102',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税普通发票(卷票)',
                    dvid: 14,
                    domainValue: '10103',
                    domainOrder: 4,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '机动车销售统一发票',
                    dvid: 15,
                    domainValue: '10104',
                    domainOrder: 5,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '二手车销售统一发票',
                    dvid: 16,
                    domainValue: '10105',
                    domainOrder: 6,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '定额发票',
                    dvid: 17,
                    domainValue: '10200',
                    domainOrder: 7,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '机打发票',
                    dvid: 18,
                    domainValue: '10400',
                    domainOrder: 8,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '出租车发票',
                    dvid: 19,
                    domainValue: '10500',
                    domainOrder: 9,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '火车票',
                    dvid: 20,
                    domainValue: '10503',
                    domainOrder: 10,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '客运汽车',
                    dvid: 21,
                    domainValue: '10505',
                    domainOrder: 11,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '航空运输电子客票行程单',
                    dvid: 22,
                    domainValue: '10506',
                    domainOrder: 12,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '过路费发票',
                    dvid: 23,
                    domainValue: '10507',
                    domainOrder: 13,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '可报销其他发票',
                    dvid: 24,
                    domainValue: '10900',
                    domainOrder: 14,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '其他',
                    dvid: 25,
                    domainValue: '00000',
                    domainOrder: 15,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '国际小票',
                    dvid: 26,
                    domainValue: '20100',
                    domainOrder: 16,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '飞机票',
                    dvid: 27,
                    domainValue: '10508',
                    domainOrder: 17,
                  },
                ],
                month: '',
                cardId: '',
                pocketName: '南京出差',
                taxAmount: 0,
                departurePlace: '',
                iid: 1046238,
                feename: '',
                invoiceState: 1,
                buyerNumber: '',
                vehicle: '其他（地铁客车出租车等）',
                arrivalPlace: '',
                certificateDtoList: [],
                ePhotoList: [
                  {
                    iid: 1046238,
                    photo_type: '4',
                    name: '电子发票附件',
                    invoice_photo_url:
                      'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAE5_7AACtM-Mj8Sc723.pdf',
                    aid: 428965,
                  },
                ],
                invoiceNumber: '46118101',
                currency: 'CNY',
                reimbursementName: '金小琪-240808-1',
                subjectName: '市内交通费',
                invoicePurpose: '客户地回公司',
                invoiceLinkList: [
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联客户',
                    dvid: 30,
                    domainValue: 'link_customer',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联销售机会',
                    dvid: 31,
                    domainValue: 'link_opportunity',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联投标',
                    dvid: 32,
                    domainValue: 'link_founds',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联项目',
                    dvid: 33,
                    domainValue: 'link_project',
                    domainOrder: 4,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联日志',
                    dvid: 34,
                    domainValue: 'link_daily',
                    domainOrder: 5,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联合同',
                    dvid: 35,
                    domainValue: 'link_contract',
                    domainOrder: 6,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联人员',
                    dvid: 36,
                    domainValue: 'link_staff',
                    domainOrder: 7,
                  },
                ],
                invoiceDate: '2024-08-08 14:44',
                invoiceLinkState: 1,
                userName: '金小琪',
                userId: 'jinxq',
                invoiceSpecial: 0,
                createTime: '2024-08-08 14:45',
                linkType: 'link_staff',
                invoiceLinkInfo: {
                  linkTypeName: '关联人员',
                  linkCode: 'anqi',
                  linkId: 1046227,
                  linkIid: 1046238,
                  linkType: 'link_staff',
                  linkDate: '',
                  linkName: '安琪',
                  linkRemarks: '',
                },
              },
            ],
            reimbursement_amount: 15.32,
            user_name: '金小琪',
            actual_amount: 15.32,
            sign: 1003,
            initial_amount: 15.32,
            reimbursement_date: '2024-08-08',
            sid: 20,
            voiceList: [1046238],
            voiceCount: 1,
            create_date: '2024-08-08 16:11:30',
            reimbursement_purpose: '客户地回公司',
            link_type_id: 1046238,
            revisability: false,
            computerFlag: '0',
            rbmDate: '',
            business_subsidy: 120,
            link_type: '0',
            bdid: 1123686,
            consolidated: 0,
            month: '',
            user_id: 'jinxq',
            business_standards: 60,
            subject_name: '市内交通费',
            invoceAggregation: {
              sumdaily: 0,
              sumfounds: 0,
              sumcustomer: 0,
              sumcontract: 0,
              sumopportunity: 0,
              sumproject: 0,
              sumstaff: 0,
            },
            bid: 331813,
          },
          {
            invoiceDetail: [
              {
                photoList: [
                  {
                    iid: 1046337,
                    photo_type: '1',
                    invoice_photo_url:
                      'http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeAEdnSAADUoGhdlXA721.jpg',
                    aid: 429052,
                  },
                ],
                invoiceTypeNew: '1',
                invoiceAmount: 24.62,
                pid: 65434,
                encryptCode: '',
                type: '',
                resolution: 0,
                sheetsNum: 1,
                subjectId: 20,
                invoicer: '',
                invoiceType: '0',
                afterTaxAmount: 0,
                happenTime: 1723046400000,
                linkCode: 'anqi',
                pocketLinkFlag: 0,
                revisability: false,
                vehicleCode: 'other',
                invoiceStateList: [
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '草稿',
                    dvid: 1,
                    domainValue: '0',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '待验证',
                    dvid: 2,
                    domainValue: '1',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '验证通过',
                    dvid: 3,
                    domainValue: '2',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'invoiceState',
                    domainValueDesc: '验证拒绝',
                    dvid: 4,
                    domainValue: '3',
                    domainOrder: 4,
                  },
                ],
                invoiceCode: '011002400111',
                buyer: '',
                checkCode: '',
                consolidated: 0,
                invoiceTypeList: [
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税专用发票',
                    dvid: 11,
                    domainValue: '10100',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税普通发票',
                    dvid: 12,
                    domainValue: '10101',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税电子普通发票',
                    dvid: 13,
                    domainValue: '10102',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '增值税普通发票(卷票)',
                    dvid: 14,
                    domainValue: '10103',
                    domainOrder: 4,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '机动车销售统一发票',
                    dvid: 15,
                    domainValue: '10104',
                    domainOrder: 5,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '二手车销售统一发票',
                    dvid: 16,
                    domainValue: '10105',
                    domainOrder: 6,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '定额发票',
                    dvid: 17,
                    domainValue: '10200',
                    domainOrder: 7,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '机打发票',
                    dvid: 18,
                    domainValue: '10400',
                    domainOrder: 8,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '出租车发票',
                    dvid: 19,
                    domainValue: '10500',
                    domainOrder: 9,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '火车票',
                    dvid: 20,
                    domainValue: '10503',
                    domainOrder: 10,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '客运汽车',
                    dvid: 21,
                    domainValue: '10505',
                    domainOrder: 11,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '航空运输电子客票行程单',
                    dvid: 22,
                    domainValue: '10506',
                    domainOrder: 12,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '过路费发票',
                    dvid: 23,
                    domainValue: '10507',
                    domainOrder: 13,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '可报销其他发票',
                    dvid: 24,
                    domainValue: '10900',
                    domainOrder: 14,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '其他',
                    dvid: 25,
                    domainValue: '00000',
                    domainOrder: 15,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '国际小票',
                    dvid: 26,
                    domainValue: '20100',
                    domainOrder: 16,
                  },
                  {
                    domainCode: 'invoiceType',
                    domainValueDesc: '飞机票',
                    dvid: 27,
                    domainValue: '10508',
                    domainOrder: 17,
                  },
                ],
                month: '',
                cardId: '',
                pocketName: '南京出差',
                taxAmount: 0,
                departurePlace: '',
                iid: 1046337,
                feename: '',
                invoiceState: 1,
                buyerNumber: '',
                vehicle: '其他（地铁客车出租车等）',
                arrivalPlace: '',
                certificateDtoList: [],
                ePhotoList: [
                  {
                    iid: 1046337,
                    photo_type: '4',
                    name: '电子发票附件',
                    invoice_photo_url:
                      'http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeARxF6AACTh2k4FAE469.pdf',
                    aid: 429053,
                  },
                ],
                invoiceNumber: '40706826',
                currency: 'CNY',
                reimbursementName: '金小琪-240808-1',
                subjectName: '市内交通费',
                invoicePurpose: '酒店到南京车站',
                invoiceLinkList: [
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联客户',
                    dvid: 30,
                    domainValue: 'link_customer',
                    domainOrder: 1,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联销售机会',
                    dvid: 31,
                    domainValue: 'link_opportunity',
                    domainOrder: 2,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联投标',
                    dvid: 32,
                    domainValue: 'link_founds',
                    domainOrder: 3,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联项目',
                    dvid: 33,
                    domainValue: 'link_project',
                    domainOrder: 4,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联日志',
                    dvid: 34,
                    domainValue: 'link_daily',
                    domainOrder: 5,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联合同',
                    dvid: 35,
                    domainValue: 'link_contract',
                    domainOrder: 6,
                  },
                  {
                    domainCode: 'linkType',
                    domainValueDesc: '关联人员',
                    dvid: 36,
                    domainValue: 'link_staff',
                    domainOrder: 7,
                  },
                ],
                invoiceDate: '2024-08-08 15:37',
                invoiceLinkState: 1,
                userName: '金小琪',
                userId: 'jinxq',
                invoiceSpecial: 0,
                createTime: '2024-08-08 15:38',
                linkType: 'link_staff',
                invoiceLinkInfo: {
                  linkTypeName: '关联人员',
                  linkCode: 'anqi',
                  linkId: 1046326,
                  linkIid: 1046337,
                  linkType: 'link_staff',
                  linkDate: '',
                  linkName: '安琪',
                  linkRemarks: '',
                },
              },
            ],
            reimbursement_amount: 24.62,
            user_name: '金小琪',
            actual_amount: 24.62,
            sign: 1004,
            initial_amount: 24.62,
            reimbursement_date: '2024-08-08',
            sid: 20,
            voiceList: [1046337],
            voiceCount: 1,
            create_date: '2024-08-08 16:11:30',
            reimbursement_purpose: '酒店到南京车站',
            link_type_id: 1046337,
            revisability: false,
            computerFlag: '0',
            rbmDate: '',
            business_subsidy: 120,
            link_type: '0',
            bdid: 1123685,
            consolidated: 0,
            month: '',
            user_id: 'jinxq',
            business_standards: 60,
            subject_name: '市内交通费',
            invoceAggregation: {
              sumdaily: 0,
              sumfounds: 0,
              sumcustomer: 0,
              sumcontract: 0,
              sumopportunity: 0,
              sumproject: 0,
              sumstaff: 0,
            },
            bid: 331813,
          },
        ],
        proportion: '',
        amountLinkSelf: 0,
        isPostSaleProject: false,
        presale: false,
        currentByUserId: {
          current: 1000,
          transfer: 0,
          subtract: 2000,
          preceding: 1000,
          isGJ: false,
        },
        LogList: [],
        assigneeMessage: [
          {
            processInstanceId: '20991329',
            messageParts: ['同意。'],
            action: 'AddComment',
            fullMessage: '同意。',
            fullMessageBytes: '5ZCM5oSP44CC',
            persistentState: 'org.activiti.engine.impl.persistence.entity.CommentEntity',
            id: '20998495',
            time: 1723124328000,
            message: '同意。',
            type: 'comment',
            userId: '安琪',
            taskId: '20991351',
          },
          {
            processInstanceId: '20991329',
            messageParts: ['同意。'],
            action: 'AddComment',
            fullMessage: '同意。',
            fullMessageBytes: '5ZCM5oSP44CC',
            persistentState: 'org.activiti.engine.impl.persistence.entity.CommentEntity',
            id: '21090115',
            time: 1723524676000,
            message: '同意。',
            type: 'comment',
            userId: '王健',
            taskId: '20998500',
          },
          {
            processInstanceId: '20991329',
            messageParts: ['同意。'],
            action: 'AddComment',
            fullMessage: '同意。',
            fullMessageBytes: '5ZCM5oSP44CC',
            persistentState: 'org.activiti.engine.impl.persistence.entity.CommentEntity',
            id: '21112645',
            time: 1724382202000,
            message: '同意。',
            type: 'comment',
            userId: '王佺',
            taskId: '21090118',
          },
          {
            processInstanceId: '20991329',
            messageParts: ['同意(其他审批环节已同意,系统默认通过)'],
            action: 'AddComment',
            fullMessage: '同意(其他审批环节已同意,系统默认通过)',
            fullMessageBytes:
              '5ZCM5oSPKOWFtuS7luWuoeaJueeOr+iKguW3suWQjOaEjyzns7vnu5/pu5jorqTpgJrov4cp',
            persistentState: 'org.activiti.engine.impl.persistence.entity.CommentEntity',
            id: '21112650',
            time: 1724382202000,
            message: '同意(其他审批环节已同意,系统默认通过)',
            type: 'comment',
            userId: '王佺',
            taskId: '21112649',
          },
        ],
        approvalPerson: ['安琪', '王健', '万云涛', '吕兴海'],
        showSales: false,
        rbtReimbusement: {
          newType: 0,
          marketingDirector: '',
          isElectronic: 0,
          userCode: '销售三部-5617',
          employeeNumber: '5617',
          businessPlace: '南京',
          isLinked: 0,
          reimbursementType: 'zl',
          result: 0,
          revocation: 0,
          reimbursementDesc: '南京投标出差',
          businessSubsidy: 120,
          businessStandards: 60,
          client: 1,
          currency: 'CNY',
          reimbursementAmount: 709.38,
          reimbursementTypeName: '差旅',
          reimbursementName: '金小琪-240808-1',
          approvalStage: '常务副总裁',
          marketingDirectorName: '',
          approvalState: 1,
          userName: '金小琪',
          currentAssignee: '周曙',
          userId: 'jinxq',
          createTime: 1723104720000,
          processInstId: '20991329',
          submissionorder: 1,
          businessDay: 2,
          position: '销售助理',
          bid: 331813,
          contracPlace: '1',
        },
        userState: 1,
        isArea: false,
        redisKey: 'jinxq:240808-1',
        invoiceMessage: [
          {
            photoList: [
              {
                iid: 1046190,
                photo_type: '1',
                invoice_photo_url:
                  'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAKD-sAAZFAdGKLAs310.jpg',
                aid: 428908,
              },
            ],
            invoiceTypeNew: '3',
            invoiceAmount: 533,
            pid: 65434,
            encryptCode: '',
            type: '',
            resolution: 0,
            sheetsNum: 1,
            subjectId: 2,
            arrivalTime: '2024-08-08',
            invoicer: '',
            invoiceType: '0',
            afterTaxAmount: 488.99,
            happenTime: 1723046400000,
            linkCode: 'anqi',
            pocketLinkFlag: 0,
            revisability: false,
            vehicleCode: 'train',
            invoiceStateList: [
              {
                domainCode: 'invoiceState',
                domainValueDesc: '草稿',
                dvid: 1,
                domainValue: '0',
                domainOrder: 1,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '待验证',
                dvid: 2,
                domainValue: '1',
                domainOrder: 2,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '验证通过',
                dvid: 3,
                domainValue: '2',
                domainOrder: 3,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '验证拒绝',
                dvid: 4,
                domainValue: '3',
                domainOrder: 4,
              },
            ],
            invoiceCode: '',
            buyer: '',
            checkCode: '',
            consolidated: 0,
            invoiceTypeList: [
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税专用发票',
                dvid: 11,
                domainValue: '10100',
                domainOrder: 1,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税普通发票',
                dvid: 12,
                domainValue: '10101',
                domainOrder: 2,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税电子普通发票',
                dvid: 13,
                domainValue: '10102',
                domainOrder: 3,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税普通发票(卷票)',
                dvid: 14,
                domainValue: '10103',
                domainOrder: 4,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '机动车销售统一发票',
                dvid: 15,
                domainValue: '10104',
                domainOrder: 5,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '二手车销售统一发票',
                dvid: 16,
                domainValue: '10105',
                domainOrder: 6,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '定额发票',
                dvid: 17,
                domainValue: '10200',
                domainOrder: 7,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '机打发票',
                dvid: 18,
                domainValue: '10400',
                domainOrder: 8,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '出租车发票',
                dvid: 19,
                domainValue: '10500',
                domainOrder: 9,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '火车票',
                dvid: 20,
                domainValue: '10503',
                domainOrder: 10,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '客运汽车',
                dvid: 21,
                domainValue: '10505',
                domainOrder: 11,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '航空运输电子客票行程单',
                dvid: 22,
                domainValue: '10506',
                domainOrder: 12,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '过路费发票',
                dvid: 23,
                domainValue: '10507',
                domainOrder: 13,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '可报销其他发票',
                dvid: 24,
                domainValue: '10900',
                domainOrder: 14,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '其他',
                dvid: 25,
                domainValue: '00000',
                domainOrder: 15,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '国际小票',
                dvid: 26,
                domainValue: '20100',
                domainOrder: 16,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '飞机票',
                dvid: 27,
                domainValue: '10508',
                domainOrder: 17,
              },
            ],
            month: '',
            cardId: '',
            goOff: '2024-08-08',
            pocketName: '南京出差',
            taxAmount: 44.01,
            departurePlace: '南京',
            iid: 1046190,
            feename: '',
            invoiceState: 1,
            buyerNumber: '',
            vehicle: '火车',
            arrivalPlace: '北京',
            certificateDtoList: [],
            ePhotoList: [],
            invoiceNumber: '',
            currency: 'CNY',
            reimbursementName: '金小琪-240808-1',
            subjectName: '差旅交通费',
            invoicePurpose: '南京回北京',
            invoiceLinkList: [
              {
                domainCode: 'linkType',
                domainValueDesc: '关联客户',
                dvid: 30,
                domainValue: 'link_customer',
                domainOrder: 1,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联销售机会',
                dvid: 31,
                domainValue: 'link_opportunity',
                domainOrder: 2,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联投标',
                dvid: 32,
                domainValue: 'link_founds',
                domainOrder: 3,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联项目',
                dvid: 33,
                domainValue: 'link_project',
                domainOrder: 4,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联日志',
                dvid: 34,
                domainValue: 'link_daily',
                domainOrder: 5,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联合同',
                dvid: 35,
                domainValue: 'link_contract',
                domainOrder: 6,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联人员',
                dvid: 36,
                domainValue: 'link_staff',
                domainOrder: 7,
              },
            ],
            invoiceDate: '2024-08-08',
            invoiceLinkState: 1,
            userName: '金小琪',
            userId: 'jinxq',
            invoiceSpecial: 0,
            createTime: '2024-08-08 14:16',
            linkType: 'link_staff',
            invoiceLinkInfo: {
              linkTypeName: '关联人员',
              linkCode: 'anqi',
              linkId: 1046179,
              linkIid: 1046190,
              linkType: 'link_staff',
              linkDate: '',
              linkName: '安琪',
              linkRemarks: '',
            },
          },
          {
            photoList: [
              {
                iid: 1046192,
                photo_type: '1',
                invoice_photo_url:
                  'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aFGATZVIAAE1CgalNIc707.png',
                aid: 428941,
              },
            ],
            invoiceTypeNew: '1',
            invoiceAmount: 16.44,
            pid: 65434,
            encryptCode: '',
            type: '',
            resolution: 0,
            sheetsNum: 1,
            subjectId: 20,
            invoicer: '',
            invoiceType: '0',
            afterTaxAmount: 0,
            happenTime: 1723046400000,
            linkCode: 'anqi',
            pocketLinkFlag: 0,
            revisability: false,
            vehicleCode: 'other',
            invoiceStateList: [
              {
                domainCode: 'invoiceState',
                domainValueDesc: '草稿',
                dvid: 1,
                domainValue: '0',
                domainOrder: 1,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '待验证',
                dvid: 2,
                domainValue: '1',
                domainOrder: 2,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '验证通过',
                dvid: 3,
                domainValue: '2',
                domainOrder: 3,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '验证拒绝',
                dvid: 4,
                domainValue: '3',
                domainOrder: 4,
              },
            ],
            invoiceCode: '',
            buyer: '',
            checkCode: '',
            consolidated: 0,
            invoiceTypeList: [
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税专用发票',
                dvid: 11,
                domainValue: '10100',
                domainOrder: 1,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税普通发票',
                dvid: 12,
                domainValue: '10101',
                domainOrder: 2,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税电子普通发票',
                dvid: 13,
                domainValue: '10102',
                domainOrder: 3,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税普通发票(卷票)',
                dvid: 14,
                domainValue: '10103',
                domainOrder: 4,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '机动车销售统一发票',
                dvid: 15,
                domainValue: '10104',
                domainOrder: 5,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '二手车销售统一发票',
                dvid: 16,
                domainValue: '10105',
                domainOrder: 6,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '定额发票',
                dvid: 17,
                domainValue: '10200',
                domainOrder: 7,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '机打发票',
                dvid: 18,
                domainValue: '10400',
                domainOrder: 8,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '出租车发票',
                dvid: 19,
                domainValue: '10500',
                domainOrder: 9,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '火车票',
                dvid: 20,
                domainValue: '10503',
                domainOrder: 10,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '客运汽车',
                dvid: 21,
                domainValue: '10505',
                domainOrder: 11,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '航空运输电子客票行程单',
                dvid: 22,
                domainValue: '10506',
                domainOrder: 12,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '过路费发票',
                dvid: 23,
                domainValue: '10507',
                domainOrder: 13,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '可报销其他发票',
                dvid: 24,
                domainValue: '10900',
                domainOrder: 14,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '其他',
                dvid: 25,
                domainValue: '00000',
                domainOrder: 15,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '国际小票',
                dvid: 26,
                domainValue: '20100',
                domainOrder: 16,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '飞机票',
                dvid: 27,
                domainValue: '10508',
                domainOrder: 17,
              },
            ],
            month: '',
            cardId: '',
            pocketName: '南京出差',
            taxAmount: 0,
            departurePlace: '',
            iid: 1046192,
            feename: '',
            invoiceState: 1,
            buyerNumber: '',
            vehicle: '其他（地铁客车出租车等）',
            arrivalPlace: '',
            certificateDtoList: [],
            ePhotoList: [
              {
                iid: 1046192,
                photo_type: '4',
                name: '电子发票附件',
                invoice_photo_url:
                  'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmATSEmAAGjO0Y0HWE662.pdf',
                aid: 428910,
              },
            ],
            invoiceNumber: '24317000000631780967',
            currency: 'CNY',
            reimbursementName: '金小琪-240808-1',
            subjectName: '市内交通费',
            invoicePurpose: '客户地回酒店',
            invoiceLinkList: [
              {
                domainCode: 'linkType',
                domainValueDesc: '关联客户',
                dvid: 30,
                domainValue: 'link_customer',
                domainOrder: 1,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联销售机会',
                dvid: 31,
                domainValue: 'link_opportunity',
                domainOrder: 2,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联投标',
                dvid: 32,
                domainValue: 'link_founds',
                domainOrder: 3,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联项目',
                dvid: 33,
                domainValue: 'link_project',
                domainOrder: 4,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联日志',
                dvid: 34,
                domainValue: 'link_daily',
                domainOrder: 5,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联合同',
                dvid: 35,
                domainValue: 'link_contract',
                domainOrder: 6,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联人员',
                dvid: 36,
                domainValue: 'link_staff',
                domainOrder: 7,
              },
            ],
            invoiceDate: '2024-08-08 14:09',
            invoiceLinkState: 1,
            userName: '金小琪',
            userId: 'jinxq',
            invoiceSpecial: 0,
            createTime: '2024-08-08 14:16',
            linkType: 'link_staff',
            invoiceLinkInfo: {
              linkTypeName: '关联人员',
              linkCode: 'anqi',
              linkId: 1046181,
              linkIid: 1046192,
              linkType: 'link_staff',
              linkDate: '',
              linkName: '安琪',
              linkRemarks: '',
            },
          },
          {
            photoList: [
              {
                iid: 1046238,
                photo_type: '1',
                invoice_photo_url:
                  'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAEMQ7AAEgbrzc8lw689.png',
                aid: 428964,
              },
            ],
            invoiceTypeNew: '1',
            invoiceAmount: 15.32,
            pid: 65434,
            encryptCode: '',
            type: '',
            resolution: 0,
            sheetsNum: 1,
            subjectId: 20,
            invoicer: '',
            invoiceType: '0',
            afterTaxAmount: 0,
            happenTime: 1723046400000,
            linkCode: 'anqi',
            pocketLinkFlag: 0,
            revisability: false,
            vehicleCode: 'other',
            invoiceStateList: [
              {
                domainCode: 'invoiceState',
                domainValueDesc: '草稿',
                dvid: 1,
                domainValue: '0',
                domainOrder: 1,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '待验证',
                dvid: 2,
                domainValue: '1',
                domainOrder: 2,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '验证通过',
                dvid: 3,
                domainValue: '2',
                domainOrder: 3,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '验证拒绝',
                dvid: 4,
                domainValue: '3',
                domainOrder: 4,
              },
            ],
            invoiceCode: '011002400111',
            buyer: '',
            checkCode: '',
            consolidated: 0,
            invoiceTypeList: [
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税专用发票',
                dvid: 11,
                domainValue: '10100',
                domainOrder: 1,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税普通发票',
                dvid: 12,
                domainValue: '10101',
                domainOrder: 2,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税电子普通发票',
                dvid: 13,
                domainValue: '10102',
                domainOrder: 3,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税普通发票(卷票)',
                dvid: 14,
                domainValue: '10103',
                domainOrder: 4,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '机动车销售统一发票',
                dvid: 15,
                domainValue: '10104',
                domainOrder: 5,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '二手车销售统一发票',
                dvid: 16,
                domainValue: '10105',
                domainOrder: 6,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '定额发票',
                dvid: 17,
                domainValue: '10200',
                domainOrder: 7,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '机打发票',
                dvid: 18,
                domainValue: '10400',
                domainOrder: 8,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '出租车发票',
                dvid: 19,
                domainValue: '10500',
                domainOrder: 9,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '火车票',
                dvid: 20,
                domainValue: '10503',
                domainOrder: 10,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '客运汽车',
                dvid: 21,
                domainValue: '10505',
                domainOrder: 11,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '航空运输电子客票行程单',
                dvid: 22,
                domainValue: '10506',
                domainOrder: 12,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '过路费发票',
                dvid: 23,
                domainValue: '10507',
                domainOrder: 13,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '可报销其他发票',
                dvid: 24,
                domainValue: '10900',
                domainOrder: 14,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '其他',
                dvid: 25,
                domainValue: '00000',
                domainOrder: 15,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '国际小票',
                dvid: 26,
                domainValue: '20100',
                domainOrder: 16,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '飞机票',
                dvid: 27,
                domainValue: '10508',
                domainOrder: 17,
              },
            ],
            month: '',
            cardId: '',
            pocketName: '南京出差',
            taxAmount: 0,
            departurePlace: '',
            iid: 1046238,
            feename: '',
            invoiceState: 1,
            buyerNumber: '',
            vehicle: '其他（地铁客车出租车等）',
            arrivalPlace: '',
            certificateDtoList: [],
            ePhotoList: [
              {
                iid: 1046238,
                photo_type: '4',
                name: '电子发票附件',
                invoice_photo_url:
                  'http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAE5_7AACtM-Mj8Sc723.pdf',
                aid: 428965,
              },
            ],
            invoiceNumber: '46118101',
            currency: 'CNY',
            reimbursementName: '金小琪-240808-1',
            subjectName: '市内交通费',
            invoicePurpose: '客户地回公司',
            invoiceLinkList: [
              {
                domainCode: 'linkType',
                domainValueDesc: '关联客户',
                dvid: 30,
                domainValue: 'link_customer',
                domainOrder: 1,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联销售机会',
                dvid: 31,
                domainValue: 'link_opportunity',
                domainOrder: 2,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联投标',
                dvid: 32,
                domainValue: 'link_founds',
                domainOrder: 3,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联项目',
                dvid: 33,
                domainValue: 'link_project',
                domainOrder: 4,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联日志',
                dvid: 34,
                domainValue: 'link_daily',
                domainOrder: 5,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联合同',
                dvid: 35,
                domainValue: 'link_contract',
                domainOrder: 6,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联人员',
                dvid: 36,
                domainValue: 'link_staff',
                domainOrder: 7,
              },
            ],
            invoiceDate: '2024-08-08 14:44',
            invoiceLinkState: 1,
            userName: '金小琪',
            userId: 'jinxq',
            invoiceSpecial: 0,
            createTime: '2024-08-08 14:45',
            linkType: 'link_staff',
            invoiceLinkInfo: {
              linkTypeName: '关联人员',
              linkCode: 'anqi',
              linkId: 1046227,
              linkIid: 1046238,
              linkType: 'link_staff',
              linkDate: '',
              linkName: '安琪',
              linkRemarks: '',
            },
          },
          {
            photoList: [
              {
                iid: 1046337,
                photo_type: '1',
                invoice_photo_url:
                  'http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeAEdnSAADUoGhdlXA721.jpg',
                aid: 429052,
              },
            ],
            invoiceTypeNew: '1',
            invoiceAmount: 24.62,
            pid: 65434,
            encryptCode: '',
            type: '',
            resolution: 0,
            sheetsNum: 1,
            subjectId: 20,
            invoicer: '',
            invoiceType: '0',
            afterTaxAmount: 0,
            happenTime: 1723046400000,
            linkCode: 'anqi',
            pocketLinkFlag: 0,
            revisability: false,
            vehicleCode: 'other',
            invoiceStateList: [
              {
                domainCode: 'invoiceState',
                domainValueDesc: '草稿',
                dvid: 1,
                domainValue: '0',
                domainOrder: 1,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '待验证',
                dvid: 2,
                domainValue: '1',
                domainOrder: 2,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '验证通过',
                dvid: 3,
                domainValue: '2',
                domainOrder: 3,
              },
              {
                domainCode: 'invoiceState',
                domainValueDesc: '验证拒绝',
                dvid: 4,
                domainValue: '3',
                domainOrder: 4,
              },
            ],
            invoiceCode: '011002400111',
            buyer: '',
            checkCode: '',
            consolidated: 0,
            invoiceTypeList: [
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税专用发票',
                dvid: 11,
                domainValue: '10100',
                domainOrder: 1,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税普通发票',
                dvid: 12,
                domainValue: '10101',
                domainOrder: 2,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税电子普通发票',
                dvid: 13,
                domainValue: '10102',
                domainOrder: 3,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '增值税普通发票(卷票)',
                dvid: 14,
                domainValue: '10103',
                domainOrder: 4,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '机动车销售统一发票',
                dvid: 15,
                domainValue: '10104',
                domainOrder: 5,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '二手车销售统一发票',
                dvid: 16,
                domainValue: '10105',
                domainOrder: 6,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '定额发票',
                dvid: 17,
                domainValue: '10200',
                domainOrder: 7,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '机打发票',
                dvid: 18,
                domainValue: '10400',
                domainOrder: 8,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '出租车发票',
                dvid: 19,
                domainValue: '10500',
                domainOrder: 9,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '火车票',
                dvid: 20,
                domainValue: '10503',
                domainOrder: 10,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '客运汽车',
                dvid: 21,
                domainValue: '10505',
                domainOrder: 11,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '航空运输电子客票行程单',
                dvid: 22,
                domainValue: '10506',
                domainOrder: 12,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '过路费发票',
                dvid: 23,
                domainValue: '10507',
                domainOrder: 13,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '可报销其他发票',
                dvid: 24,
                domainValue: '10900',
                domainOrder: 14,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '其他',
                dvid: 25,
                domainValue: '00000',
                domainOrder: 15,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '国际小票',
                dvid: 26,
                domainValue: '20100',
                domainOrder: 16,
              },
              {
                domainCode: 'invoiceType',
                domainValueDesc: '飞机票',
                dvid: 27,
                domainValue: '10508',
                domainOrder: 17,
              },
            ],
            month: '',
            cardId: '',
            pocketName: '南京出差',
            taxAmount: 0,
            departurePlace: '',
            iid: 1046337,
            feename: '',
            invoiceState: 1,
            buyerNumber: '',
            vehicle: '其他（地铁客车出租车等）',
            arrivalPlace: '',
            certificateDtoList: [],
            ePhotoList: [
              {
                iid: 1046337,
                photo_type: '4',
                name: '电子发票附件',
                invoice_photo_url:
                  'http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeARxF6AACTh2k4FAE469.pdf',
                aid: 429053,
              },
            ],
            invoiceNumber: '40706826',
            currency: 'CNY',
            reimbursementName: '金小琪-240808-1',
            subjectName: '市内交通费',
            invoicePurpose: '酒店到南京车站',
            invoiceLinkList: [
              {
                domainCode: 'linkType',
                domainValueDesc: '关联客户',
                dvid: 30,
                domainValue: 'link_customer',
                domainOrder: 1,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联销售机会',
                dvid: 31,
                domainValue: 'link_opportunity',
                domainOrder: 2,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联投标',
                dvid: 32,
                domainValue: 'link_founds',
                domainOrder: 3,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联项目',
                dvid: 33,
                domainValue: 'link_project',
                domainOrder: 4,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联日志',
                dvid: 34,
                domainValue: 'link_daily',
                domainOrder: 5,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联合同',
                dvid: 35,
                domainValue: 'link_contract',
                domainOrder: 6,
              },
              {
                domainCode: 'linkType',
                domainValueDesc: '关联人员',
                dvid: 36,
                domainValue: 'link_staff',
                domainOrder: 7,
              },
            ],
            invoiceDate: '2024-08-08 15:37',
            invoiceLinkState: 1,
            userName: '金小琪',
            userId: 'jinxq',
            invoiceSpecial: 0,
            createTime: '2024-08-08 15:38',
            linkType: 'link_staff',
            invoiceLinkInfo: {
              linkTypeName: '关联人员',
              linkCode: 'anqi',
              linkId: 1046326,
              linkIid: 1046337,
              linkType: 'link_staff',
              linkDate: '',
              linkName: '安琪',
              linkRemarks: '',
            },
          },
        ],
        kjrcwb: 0,
        afterSale: false,
        certificatesMessage: [],
        consolidatedInvoices: [],
      },
    };
  },
  mounted() {
    // const dataJson = require('./data.json');
    console.log(dataJson, 'dataJson');
  },
  methods: {
    // 前分为显示逗号
    toThousands: function (num) {
      num = num * 1;
      var num_str = num.toFixed(2).split('.')[0];
      return (
        (num_str || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') +
        '.' +
        num.toFixed(2).split('.')[1]
      );
    },
    //获取币种
    getCurrencyUnit(val) {
      const foundOption = this.currencylist.find(option => option.currency == val);
      return foundOption ? foundOption.unit : '元';
    },
    //时间戳转换为普通格式
    formatTime(value, type) {
      let dataTime = '';
      let data = new Date();
      data.setTime(value);
      let year = data.getFullYear();
      let month = data.getMonth() + 1;
      let day = data.getDate();
      let hour = data.getHours();
      let minute = data.getMinutes();
      let second = data.getSeconds();

      month < 10 ? (month = '0' + month) : month;
      day < 10 ? (day = '0' + day) : day;
      hour < 10 ? (hour = '0' + hour) : hour;
      minute < 10 ? (minute = '0' + minute) : minute;
      second < 10 ? (second = '0' + second) : second;

      if (type == 'YMD') {
        dataTime = year + '-' + month + '-' + day;
      } else if (type == 'NYR') {
        dataTime = year + '年' + month + '月' + day + '日';
      } else if (type == 'YMDHMS') {
        dataTime = year + '-' + month + '-' + day + '  ' + hour + ':' + minute + ':' + second;
      } else if (type == 'YMDHM') {
        dataTime = year + '-' + month + '-' + day + '  ' + hour + ':' + minute;
      } else if (type == 'HMS') {
        dataTime = hour + ':' + minute + ':' + second;
      } else if (type == 'YM') {
        dataTime = year + '-' + month + '-';
      }
      return dataTime; // 将格式化后的字符串输出到前端显示
    },
    approvalResult() {
      this.allMinData = result.data.data.data;
      this.LogList = result.data.data.LogList;
      this.allMinData.map(val => {
        //手机话费21，电脑补助29，邮运费18时候，销售可以选择0,添加 20 市内交通费,添加 30 入职体检
        if (
          val.sid == '29' ||
          val.sid == '21' ||
          val.sid == '18' ||
          val.sid == '15' ||
          val.sid == '20' ||
          val.sid == '2' ||
          val.sid == '30'
        ) {
          // this.zeroMarketing = true;
          this.iszeroList.push(true);
          console.log(val.invoiceDetail[0].linkType + '111111111222222222');
          console.log(val.invoiceDetail[0].linkType == 'link_project');
          if (val.invoiceDetail[0].linkType == 'link_project') {
            this.projectState = true;
          }
        } else {
          // this.zeroMarketing = false;
          this.iszeroList.push(false);
        }
        if (this.iszeroList.indexOf(false) > -1) {
          this.zeroMarketing = false;
        } else {
          this.zeroMarketing = true;
        }
        //住宿费 7 差旅交通费2 市内交通费20时候，销售可以选择0,
        if (val.sid != '2' && val.sid != '7' && val.sid != '20') {
          this.iszeroList1.push(false);
        } else {
          this.iszeroList1.push(true);
          if (val.invoiceDetail[0].linkType == 'link_project') {
            this.projectState = true;
          }
        }
        if (this.iszeroList1.indexOf(false) > -1) {
          this.zeroMarketing1 = false;
        } else {
          this.zeroMarketing1 = true;
        }
        //15为福利费，为核酸检测提供无费用承担人控制
        if (val.sid == '15') {
          this.zeroCostPayer = true;
        }
        if (val.sid == '21') {
          this.showPhone = true;
        }
        if (val.subject_name == '电脑补助') {
          this.computerMoney = (Number(this.computerMoney) + Number(val.actual_amount)).toFixed(2);
        }
        if (val.subject_name == '入职体检') {
          tjnum++;
        }
        if (val.subject_name == '培训费') {
          px = true;
        }
      });
      this.tableData.map(item => {
        if (item.subject_name == '差旅交通费') {
          item.invoiceDetail.length > 0 &&
            (sumT1 = Number(item.invoiceDetail[0].taxAmount) + sumT1);
          item.invoiceDetail.length > 0 &&
            (sumT2 = Number(item.invoiceDetail[0].afterTaxAmount) + sumT2);
          this.travelList.push(item);
        } else if (item.subject_name == '住宿费') {
          item.invoiceDetail.length > 0 &&
            (sumH1 = Number(item.invoiceDetail[0].taxAmount) + sumH1);
          item.invoiceDetail.length > 0 &&
            (sumH2 = Number(item.invoiceDetail[0].afterTaxAmount) + sumH2);
          this.hotelList.push(item);
        } else {
          item.invoiceDetail.length > 0 &&
            (sumP1 = Number(item.invoiceDetail[0].taxAmount) + sumP1);
          item.invoiceDetail.length > 0 &&
            (sumP2 = Number(item.invoiceDetail[0].afterTaxAmount) + sumP2);
          this.plainList.push(item);
        }
        // 打回电脑补-明细中存在电脑补，同时也存在其他科目事，显示打回电脑补按钮
        if (item.sid == 29) {
          haveCom = true;
        } else if (item.sid != 2 && item.sid != 7) {
          haveOther = true;
        }
      });
      this.approvalStage = result.data.data.rbtReimbusement.approvalStage;
      this.travelTaxSum = sumT1;
      this.travelAfterTaxSum = sumT2;
      this.hotelTaxSum = sumH1;
      this.hotelAfterTaxSum = sumH2;
      this.plainTaxSum = sumP1;
      this.plainAfterTaxSum = sumP2;
    },

    getForm(processId, processDefKey) {
      let param;
      let method;
      if (processId) {
        param = processId;
        method = 'getStartForm';
      } else if (processDefKey) {
        param = processDefKey;
        method = 'getStartFormByProcessDefKey';
      }
      this[method](param).then(res => {
        console.log(this.option, 'option');
        let { process, startForm } = res;
        this.form.processId = process.id;
        const option = this.option;
        const { column, group } = option;

        const groupArr = [];
        const columnArr = this.filterAvueColumn(column, startForm, true).column;
        if (group && group.length > 0) {
          // 处理group
          group.forEach(gro => {
            gro.column = this.filterAvueColumn(gro.column, startForm, true).column;
            if (gro.column.length > 0) groupArr.push(gro);
          });
        }

        option.column = columnArr;
        option.group = groupArr;
        this.option = option;

        if (this.permission.wf_process_draft) {
          console.log(this.option, 'this.option');
          // 查询是否有草稿箱
          this.initDraft({ processDefId: process.id }).then(data => {
            this.$confirm('是否恢复之前保存的草稿？', '提示', {})
              .then(() => {
                this.form = JSON.parse(data);
              })
              .catch(() => { });
          });
        }

        this.waiting = false;
      });
    },
    handleSubmit() {
      let form = this.deepClone(this.form);
      form = {
        ...form,
        processDefId: form.processId,
        exFormKey: this.process.formKey,
        exFormUrl: this.process.formUrl,
      };
      submit(form).then(res => {
        const data = res.data.data;
        this.form.processDefKey = this.process.key;
        this.form.businessKey = data.id;
        this.handleStartProcessByKey(true)
          .then((res, done) => {
            const processInsId = res.data.data;
            submit({ id: data.id, processInsId }).then(() => {
              this.$message.success('发起成功');
              this.handleCloseTag('/plugin/workflow/pages/process/send');
              if (typeof done == 'function') done();
            });
          })
          .catch(() => {
            this.loading = false;
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 5px;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
<style lang="scss" scoped>
// @import url("../../assets/fonts(1)/iconfont.css");
.header {
  height: 50px;
  display: flex;
  align-items: center;
  background: #fff;
  border-top: solid 1px #e6e6e6;
  position: relative;

  .goBack {
    cursor: pointer;

    span {
      color: #297cba;

      &:nth-child(2) {
        font-size: 16px;
        border-right: 1px solid #ccc;
        padding-right: 10px;
      }

      i {
        color: #297cba;
        font-size: 16px;
      }
    }
  }

  .check {
    padding-left: 10px;
    font-size: 16px;
  }
}

.dialog {
  .info {
    width: 100%;
    padding: 0 30%;
    box-sizing: border-box;
    margin-top: -30px;
    margin-bottom: -30px;

    p {
      display: flex;
      align-items: center;
      margin: 10px 0;

      .lable-info {
        width: 110px;
      }

      .info-right {
        display: inline-block;
        flex: 1;
      }
    }
  }
}

// .dialog-footer{
//   margin-top: -30px !important;
// }

.detail {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-right-active {
  color: red;
}

.cpu-box {
  width: 100%;
  padding: 0 200px 0 15px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.zlinfo {
  width: 100%;
  display: flex;
  padding: 10px 10px;
  box-sizing: border-box;
  align-items: center;
  font-size: 16px;
  color: #333;
  border: 1px solid #eee;
  margin: 20px 0;

  .spn,
  p {
    display: inline-block;
    flex: 1;
  }

  .setspn {
    color: #fff;
    padding: 7px 17px;
    border-radius: 5px;
    display: inline-block;
    background: #409eff;
  }

  .onsetspn {
    background: #e6a23c;
    margin-right: 15px;
  }
}

.app-container {
  width: 96%;
  height: 100%;
  margin: 20px auto;
  padding: 0;
  background: #fff;
}

.content {
  padding: 0 20px;

  .essentialInfo {
    width: 100%;
    font-size: 12px;
    font-weight: 600;
    color: #000;

    div {
      height: 40px;
      line-height: 40px;
    }
  }

  .info {
    ul {
      padding: 0;
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #ebeef5;

      li {
        width: 33.3%;
        padding: 0;
        margin: 0;
        list-style-type: none;
        display: flex;
        align-items: center;
        font-size: 16px;

        &>div {
          padding-left: 20px;
          font-size: 16px;
          display: flex;
          width: 100px;
          padding: 10px;

          &>span {
            text-align-last: justify;
            flex: 1;
            display: inline-block;
          }
        }

        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5),
        &:nth-child(6) {
          border-bottom: 1px solid #ebeef5;
        }

        &:last-child {
          width: 100%;
          border-top: 1px solid #ebeef5;

          div {
            padding: 10px;
          }
        }
      }
    }
  }

  .ordinary {
    font-size: 12px;
    color: #000;
    display: flex;
    margin: 20px 0;

    div {
      padding: 12px 15px;
      border-radius: 5px;
      font-weight: 600;
      margin-right: 20px;
    }

    .ordinaryActive {
      color: #fff;

      background: #409eff;
    }
  }

  .computer_block {
    display: flex;
    justify-content: space-between;
    width: 60%;
    margin-left: 15px;
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 30px;

    span {
      color: red;
    }
  }

  .approvalLog {
    width: 100%;
    font-size: 12px;
    font-weight: 600;
    color: #000;

    div {
      height: 40px;
      line-height: 40px;
    }
  }
}

.select {
  span {
    color: #000;
    font-size: 12px;
    font-weight: 600;
  }
}

.afterSale {
  .select {
    display: flex;
    align-items: center;
    margin: 15px 0;

    span {
      color: #000;
      font-size: 12px;
      font-weight: 600;
    }
  }
}

.ordinaryDetails {
  border: 1px #ebeef5 solid;
  border-bottom: 0;

  &:last-child {
    border-bottom: 0;
  }

  .el-tag.el-tag--warning {
    background-color: #ffe6c2;
    border-color: #ffe9d6;
    color: #bf7507;
  }
}

.ordinaryDetail {
  margin-bottom: 15px;
}

.result {
  width: 95%;
  margin: 20px auto;

  .btn {
    width: 90px;
  }
}

.purpose {
  display: inline-block;
  width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.active {
  border: 1px solid blue !important;
  background: blue;
}

.taxBox {
  display: inline-block;
  width: 10px;
  height: 10px;
  border: 1px solid #ccc;
}

.reimbursementDesc {
  display: inline-block;
  width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dz_icon {
  right: -10px;
  top: -50px;
  position: absolute;
  width: 80px;
  transform: rotate(20deg);
}
</style>
