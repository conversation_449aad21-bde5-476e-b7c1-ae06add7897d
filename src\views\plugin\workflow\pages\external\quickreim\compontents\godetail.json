{"code": "1", "data": {"photoList": [], "invoiceTypeNew": "3", "invoiceAmount": 22, "pid": 62417, "encryptCode": "", "type": "", "sheetsNum": 1, "resolution": 0, "subjectId": 2, "arrivalTime": "2024-08-15", "invoicer": "", "invoiceType": "0", "afterTaxAmount": 20.18, "happenTime": 1723651200000, "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "train", "invoiceStateList": [{"dvid": 1, "domainCode": "invoiceState", "domainValueDesc": "草稿", "domainValue": "0", "domainOrder": 1}, {"dvid": 2, "domainCode": "invoiceState", "domainValueDesc": "待验证", "domainValue": "1", "domainOrder": 2}, {"dvid": 3, "domainCode": "invoiceState", "domainValueDesc": "验证通过", "domainValue": "2", "domainOrder": 3}, {"dvid": 4, "domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "domainValue": "3", "domainOrder": 4}], "invoiceCode": "", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"dvid": 11, "domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "domainValue": "10100", "domainOrder": 1}, {"dvid": 12, "domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "domainValue": "10101", "domainOrder": 2}, {"dvid": 13, "domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "domainValue": "10102", "domainOrder": 3}, {"dvid": 14, "domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "domainValue": "10103", "domainOrder": 4}, {"dvid": 15, "domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "domainValue": "10104", "domainOrder": 5}, {"dvid": 16, "domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "domainValue": "10105", "domainOrder": 6}, {"dvid": 17, "domainCode": "invoiceType", "domainValueDesc": "定额发票", "domainValue": "10200", "domainOrder": 7}, {"dvid": 18, "domainCode": "invoiceType", "domainValueDesc": "机打发票", "domainValue": "10400", "domainOrder": 8}, {"dvid": 19, "domainCode": "invoiceType", "domainValueDesc": "出租车发票", "domainValue": "10500", "domainOrder": 9}, {"dvid": 20, "domainCode": "invoiceType", "domainValueDesc": "火车票", "domainValue": "10503", "domainOrder": 10}, {"dvid": 21, "domainCode": "invoiceType", "domainValueDesc": "客运汽车", "domainValue": "10505", "domainOrder": 11}, {"dvid": 22, "domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "domainValue": "10506", "domainOrder": 12}, {"dvid": 23, "domainCode": "invoiceType", "domainValueDesc": "过路费发票", "domainValue": "10507", "domainOrder": 13}, {"dvid": 24, "domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "domainValue": "10900", "domainOrder": 14}, {"dvid": 25, "domainCode": "invoiceType", "domainValueDesc": "其他", "domainValue": "00000", "domainOrder": 15}, {"dvid": 26, "domainCode": "invoiceType", "domainValueDesc": "国际小票", "domainValue": "20100", "domainOrder": 16}, {"dvid": 27, "domainCode": "invoiceType", "domainValueDesc": "飞机票", "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "goOff": "2024-08-15", "taxAmount": 1.82, "pocketName": "默认票夹", "departurePlace": "22", "ephotoList": [], "iid": 1049047, "feename": "", "invoiceState": 2, "buyerNumber": "", "vehicle": "火车", "arrivalPlace": "22", "certificateDtoList": [], "invoiceNumber": "", "currency": "CNY", "reimbursementName": "孙忠全-240815-4", "subjectName": "差旅交通费", "invoicePurpose": "2", "invoiceLinkList": [{"dvid": 30, "domainCode": "linkType", "domainValueDesc": "关联客户", "domainValue": "link_customer", "domainOrder": 1}, {"dvid": 31, "domainCode": "linkType", "domainValueDesc": "关联销售机会", "domainValue": "link_opportunity", "domainOrder": 2}, {"dvid": 32, "domainCode": "linkType", "domainValueDesc": "关联投标", "domainValue": "link_founds", "domainOrder": 3}, {"dvid": 33, "domainCode": "linkType", "domainValueDesc": "关联项目", "domainValue": "link_project", "domainOrder": 4}, {"dvid": 34, "domainCode": "linkType", "domainValueDesc": "关联日志", "domainValue": "link_daily", "domainOrder": 5}, {"dvid": 35, "domainCode": "linkType", "domainValueDesc": "关联合同", "domainValue": "link_contract", "domainOrder": 6}, {"dvid": 36, "domainCode": "linkType", "domainValueDesc": "关联人员", "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-15", "invoiceLinkState": 1, "userName": "孙忠全", "userId": "sunzhongq", "invoiceSpecial": 0, "createTime": "2024-08-15 10:51", "invoiceLinkInfo": {"linkTypeName": "关联客户", "linkCode": "2407161637390933", "linkId": 1049036, "linkIid": 1049047, "linkType": "link_customer", "linkName": "中电建核电工程", "linkDate": "", "linkRemarks": ""}}, "msg": "成功"}