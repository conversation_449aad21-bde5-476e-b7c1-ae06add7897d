<template>
  <nf-container>
    <el-skeleton :loading="waiting" :rows="8">
      <h3 style="margin-bottom: 20px">{{ process.name }}</h3>
      <el-card shadow="never" style="margin-top: 20px">
        <nf-form
          v-if="
            option &&
            ((option.column && option.column.length > 0) ||
              (option.group && option.group.length > 0))
          "
          v-model="form"
          ref="form"
          :option="option"
          v-model:defaults="defaults"
          :upload-preview="handleUploadPreview"
        >
        </nf-form>
      </el-card>

      <el-card shadow="never" style="margin-top: 20px" v-if="showExamForm">
        <nf-examine-form
          ref="examineForm"
          :process="process"
          @user-select="handleUserSelect"
        ></nf-examine-form>
      </el-card>
      <div style="height: 80px"></div>
      <el-affix position="bottom" :offset="20">
        <el-row
          class="foot-item"
          :style="{ width: isCollapse ? 'calc(100% - 71px)' : 'calc(100% - 241px)' }"
        >
          <el-button type="primary" size="default" v-loading="loading" @click="handleStartProcess">
            发起
          </el-button>
          <el-button
            v-if="permission.wf_process_draft"
            type="success"
            size="default"
            v-loading="loading"
            @click="
              handleDraft({ processDefId: process.id, formKey: process.formKey, variables: form })
            "
          >
            存为草稿
          </el-button>
        </el-row>
      </el-affix>
    </el-skeleton>

    <!-- 人员选择弹窗 -->
    <nf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      :user-select-type="userSelectType"
      @onConfirm="handleUserSelectConfirm"
    ></nf-user-select>
  </nf-container>
</template>

<script>
import NfExamineForm from '../../../components/nf-exam-form/index.vue';
import NfUserSelect from '../../../components/nf-user-select/index.vue';
import exForm from '../../../mixins/ex-form';
import draft from '../../../mixins/draft';

export default {
  components: {
    NfUserSelect,
    NfExamineForm,
  },
  mixins: [exForm, draft],
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(window.atob(val));
          const { processId, processDefKey, params } = param;
          if (processId || processDefKey) this.getForm(processId, processDefKey);
          if (params) this.params = params;
        }
      },
      immediate: true,
    },
  },
  computed: {
    showExamForm() {
      const { hideComment, hideCopy, hideExamine } = this.process;
      return !hideComment || !hideCopy || !hideExamine;
    },
  },
  data() {
    return {
      defaults: {},
      form: {},
      option: {},
      process: {},
      loading: false,
      params: null,
    };
  },
  methods: {
    getForm(processId, processDefKey) {
      let param;
      let method;
      if (processId) {
        param = processId;
        method = 'getStartForm';
      } else if (processDefKey) {
        param = processDefKey;
        method = 'getStartFormByProcessDefKey';
      }
      this[method](param).then(res => {
        let { process, form, startForm } = res;
        this.form.processId = process.id;
        if (form) {
          const option = { ...Function('"use strict";return (' + form + ')')(), menuBtn: false };
          const { column, group } = option;

          const groupArr = [];
          const columnArr = this.filterAvueColumn(column, startForm).column;
          if (group && group.length > 0) {
            // 处理group
            group.forEach(gro => {
              gro.column = this.filterAvueColumn(gro.column, startForm).column;
              if (gro.column.length > 0) groupArr.push(gro);
            });
          }

          option.column = columnArr;
          option.group = groupArr;
          this.option = option;

          if (!this.validatenull(this.params)) this.form = { ...this.form, ...this.params };

          if (this.permission.wf_process_draft) {
            // 查询是否有草稿箱
            this.initDraft({ processDefId: process.id }).then(data => {
              this.$confirm('是否恢复之前保存的草稿？', '提示', {})
                .then(() => {
                  this.form = JSON.parse(data);
                })
                .catch(() => {});
            });
          }
        }
        this.waiting = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.foot-item {
  position: fixed;
  bottom: 5px;
  margin-left: -20px;
  // right: 0;
  z-index: 101;
  height: 66px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
