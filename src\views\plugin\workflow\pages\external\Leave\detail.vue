<template>
  <nf-container>
    <el-skeleton :loading="waiting" avatar :rows="8">
      <el-affix position="top" :offset="110">
        <div class="header">
          <h3>{{ process.processDefinitionName }}</h3>
          <div style="display: flex">
            <nf-theme
              v-if="process.status != 'todo'"
              v-model="theme"
              :theme-list="themeList"
            ></nf-theme>
            <nf-form-variable :process-ins-id="process.processInstanceId"></nf-form-variable>
          </div>
        </div>
      </el-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div id="printBody" :class="process.status != 'todo' ? `nf-theme-custom` : ''">
              <!-- <nf-form
                v-if="
                  option &&
                  ((option.column && option.column.length > 0) ||
                    (option.group && option.group.length > 0))
                "
                v-model="form"
                :style="themeCustomStyle"
                ref="form"
                v-model:defaults="defaults"
                :option="option"
                :upload-preview="handleUploadPreview"
              >
              </nf-form> -->
              <!-- <el-form :model="form" label-width="auto"> -->
              <!-- <el-row>
                <el-col :span="12">
                  <div style="width: 100%; height: 22px">
                    <span
                      style="
                        width: 120px;
                        background: rgba(250, 250, 250, 1);
                        display: inline-block;
                      "
                      >Activity name</span
                    >
                    <span style="">Activity name</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <span>Activity name</span>
                  <span>Activity name</span>
                </el-col>
              </el-row> -->
              <!-- </el-form> -->
              <!-- <el-descriptions title="Width horizontal list" border :column="2">
                <el-descriptions-item label="Username">kooriookami</el-descriptions-item>
                <el-descriptions-item label="Telephone">18100000000</el-descriptions-item>
                <el-descriptions-item label="Place">Suzhou</el-descriptions-item>
                <el-descriptions-item label="Address">
                  No.1188, Wuzhong Avenue, Wuzhong District, Suzhou, Jiangsu Province
                </el-descriptions-item>
              </el-descriptions> -->
              <template v-if="process.status == 'todo'">
                <el-form
                  :inline="true"
                  :model="form"
                  class="demo-form-inline"
                  label-width="120px"
                  label-position="left"
                  ref="form"
                >
                  <el-form-item label="请假类型：" props="vacationtypevalue">
                    <el-input v-model="form.vacationtypevalue" placeholder="" disabled />
                  </el-form-item>
                  <el-form-item label="请假时长：" props="daytime">
                    <el-input :value="`${form.daytime}天`" placeholder="" disabled />
                  </el-form-item>
                  <el-form-item label="开始时间：">
                    <el-input
                      :value="`${form.starttime}${form.starttype == 0 ? '上午' : '下午'}`"
                      placeholder=""
                      disabled
                    />
                  </el-form-item>
                  <el-form-item label="结束时间：">
                    <el-input
                      :value="`${form.endtime}${form.endtype == 0 ? '上午' : '下午'}`"
                      placeholder=""
                      disabled
                    />
                  </el-form-item>
                  <el-form-item label="请假事由：" style="width: 98%">
                    <el-input v-model="form.reason" placeholder="" type="textarea" disabled />
                  </el-form-item>
                  <el-form-item label="说明附件：" style="width: 98%">
                    <!-- <el-input v-model="formInline.user" placeholder="" type="textarea" /> -->
                    <template v-if="form.filePaths && form.filePaths.length > 0">
                      <el-image
                        v-for="(item, index) in form.filePaths"
                        :key="index"
                        style="width: 100px; height: 100px"
                        :src="item"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        :preview-src-list="form.filePaths"
                        :initial-index="4"
                        fit="cover"
                      />
                    </template>
                  </el-form-item>
                </el-form>
              </template>
              <template v-else>
                <el-descriptions title="" border :column="2" label-width="120px">
                  <el-descriptions-item label="请假类型：">{{
                    form.vacationtypevalue
                  }}</el-descriptions-item>
                  <el-descriptions-item label="请假时长："
                    >{{ form.daytime }}天</el-descriptions-item
                  >
                  <el-descriptions-item label="开始时间："
                    >{{ form.starttime
                    }}{{ form.starttype == 0 ? '上午' : '下午' }}</el-descriptions-item
                  >
                  <el-descriptions-item label="结束时间：">
                    {{ form.endtime }}{{ form.endtype == 0 ? '上午' : '下午' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="请假事由：" :span="18">
                    {{ form.reason }}
                  </el-descriptions-item>
                  <el-descriptions-item label=" 说明附件：" :span="18">
                    <template v-if="form.filePaths && form.filePaths.length > 0">
                      <el-image
                        v-for="(item, index) in form.filePaths"
                        :key="index"
                        style="width: 100px; height: 100px"
                        :src="item"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        :preview-src-list="form.filePaths"
                        :initial-index="4"
                        fit="cover"
                      />
                    </template>
                  </el-descriptions-item>
                </el-descriptions>
              </template>
              <el-card
                shadow="never"
                style="margin-top: 10px"
                header="流转信息"
                :body-style="{ padding: '20px 0' }"
                v-if="isPrintShow"
              >
                <nf-flow :flow-list="flow"></nf-flow>
              </el-card>
            </div>
          </el-card>
          <el-card shadow="never" style="margin-top: 20px" v-if="process.status == 'todo'">
            <nf-examine-form
              ref="examineForm"
              v-model:comment="comment"
              :process="process"
              @user-select="handleUserSelect"
            ></nf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <nf-flow :flow-list="flow"></nf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName == 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <nf-design ref="bpmn" style="height: 500px" :options="bpmnOption"></nf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-skeleton>

    <!-- 底部按钮 -->
    <nf-button
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @print="handlePrint"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></nf-button>
    <!-- 人员选择弹窗 -->
    <nf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      :user-select-type="userSelectType"
      @onConfirm="handleUserSelectConfirm"
    ></nf-user-select>
  </nf-container>
</template>

<script>
import NfExamineForm from '../../../components/nf-exam-form/index.vue';
import NfButton from '../../../components/nf-button/index.vue';
import NfFlow from '../../../components/nf-flow/index.vue';
import NfTheme from '../../../components/nf-theme/index.vue';
import NfFormVariable from '../../../components/nf-form-variable/index.vue';
import NfUserSelect from '../../../components/nf-user-select/index.vue';

import exForm from '../../../mixins/ex-form';
import theme from '../../../mixins/theme';

export default {
  mixins: [exForm, theme],
  components: { NfUserSelect, NfExamineForm, NfButton, NfFlow, NfTheme, NfFormVariable },
  watch: {
    '$route.query.p': {
      handler(val) {
        if (val) {
          const param = JSON.parse(window.atob(val));
          const { taskId, processInsId } = param;
          if ((taskId && processInsId) || processInsId) this.getDetail(taskId, processInsId);
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      // option: {
      //   column: [
      //     {
      //       type: 'input',
      //       label: '创建人',
      //       span: 12,
      //       display: true,
      //       prop: 'creator',
      //       value: '${this.$store.getters.userInfo.nick_name}',
      //       readonly: true,
      //     },
      //     {
      //       type: 'input',
      //       label: '创建部门',
      //       span: 12,
      //       display: true,
      //       prop: 'creatorDept',
      //       value: '${this.$store.getters.userInfo.dept_name}',
      //       readonly: true,
      //     },
      //     {
      //       type: 'datetimerange',
      //       label: '请假时间',
      //       span: 12,
      //       display: true,
      //       format: 'YYYY-MM-DD HH:mm:ss',
      //       valueFormat: 'YYYY-MM-DD HH:mm:ss',
      //       prop: 'datetime',
      //       dataType: 'string',
      //       required: true,
      //       rules: [
      //         {
      //           required: true,
      //           message: '开始时间必须填写',
      //         },
      //       ],
      //       change: ({ value }) => {
      //         if (!value || value.length == 0) {
      //           this.form.days = undefined;
      //         } else {
      //           const d1 = Date.parse(value.split(',')[0]);
      //           const d2 = Date.parse(value.split(',')[1]);
      //           const day = (d2 - d1) / (1 * 24 * 60 * 60 * 1000);
      //           this.form.days = Number(day.toFixed(2));
      //         }
      //       },
      //     },
      //     {
      //       type: 'number',
      //       label: '请假天数',
      //       span: 12,
      //       display: true,
      //       prop: 'days',
      //       required: true,
      //       rules: [
      //         {
      //           required: true,
      //           message: '请假天数必须填写',
      //         },
      //       ],
      //       controls: true,
      //       controlsPosition: 'right',
      //       // change: ({ value }) => {
      //       //   if (value) this.form.reason = '请假' + value + '天';
      //       //   else this.form.reason = '';
      //       // },
      //     },
      //     {
      //       type: 'textarea',
      //       label: '请假理由',
      //       span: 24,
      //       display: true,
      //       prop: 'reason',
      //       required: true,
      //       rules: [
      //         {
      //           required: true,
      //           message: '请假理由必须填写',
      //         },
      //       ],
      //     },
      //     {
      //       label: '附件',
      //       type: 'upload',
      //       propsHttp: {
      //         res: 'data',
      //         url: 'link',
      //         name: 'originalName',
      //       },
      //       action: '/blade-resource/oss/endpoint/put-file',
      //       display: true,
      //       span: 24,
      //       showFileList: true,
      //       multiple: true,
      //       limit: 10,
      //       prop: 'attachment',
      //       dataType: 'string',
      //     },
      //   ],
      // },
      option: {
        column: [
          {
            type: 'input',
            label: '请假类型',
            span: 12,
            display: true,
            prop: 'vacationtypevalue',
            // value: '${this.$store.getters.userInfo.nick_name}',
            readonly: true,
          },
          {
            type: 'input',
            label: '开始时间',
            span: 12,
            display: true,
            prop: 'starttime',
            value: '${starttime}',
            readonly: true,
          },
          {
            type: 'input',
            label: '',
            span: 12,
            display: true,
            prop: 'starttype',
            readonly: true,
          },
          {
            type: 'input',
            label: '结束时间',
            span: 12,
            display: true,
            prop: 'endtime',
            value: '${endtime}',
            readonly: true,
          },
          {
            type: 'input',
            label: '',
            span: 12,
            display: true,
            prop: 'endtype',
            readonly: true,
          },
          {
            type: 'input',
            label: '请假时长',
            span: 12,
            display: true,
            prop: 'daytime',
            value: '${daytime}天',
            readonly: true,
          },
          {
            type: 'textarea',
            label: '请假理由',
            span: 24,
            display: true,
            prop: 'reason',
            required: true,
            value: '${reason}',
            readonly: true,
            rules: [
              {
                required: true,
                message: '请假理由必须填写',
              },
            ],
          },
          {
            label: '附件',
            type: 'upload',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName',
            },
            action: '/blade-resource/oss/endpoint/put-file',
            display: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 10,
            prop: 'filePaths',
            dataType: 'string',
          },
        ],
      },
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      formInline: {
        user: '',
        region: '',
        date: '',
      },
    };
  },
  methods: {
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        const { process, form } = res;

        const { variables, status } = process;

        let { taskForm } = form;
        const option = this.option;
        option.menuBtn = false;
        let { column, group } = option;
        if (taskForm && taskForm.length > 0) {
          const columnFilter = this.filterAvueColumn(column, taskForm, true);
          column = columnFilter.column;
          let vars = columnFilter.vars || [];

          const groupArr = [];
          if (group && group.length > 0) {
            // 处理group
            group.forEach(gro => {
              const groupFilter = this.filterAvueColumn(gro.column, taskForm, true);
              gro.column = groupFilter.column;
              vars = vars.concat(groupFilter.vars);
              if (gro.column.length > 0) groupArr.push(gro);
            });
          }
          group = groupArr;
          this.vars = vars;
        }

        if (status != 'todo') {
          // 已办，删除字段默认值
          option.detail = true;
          if (column && column.length > 0) {
            // 处理column
            column.forEach(col => {
              if (col.type == 'dynamic')
                col.children.column.forEach(cc => {
                  delete cc.value;
                });
              delete col.value;
            });
          }

          if (group && group.length > 0) {
            // 处理group
            group.forEach(gro => {
              if (gro.column && gro.column.length > 0) {
                gro.column.forEach(col => {
                  if (col.type == 'dynamic')
                    col.children.column.forEach(cc => {
                      delete cc.value;
                    });
                  delete col.value;
                });
              }
            });
          }
        }
        option.column = column;
        option.group = group;

        // for (let key in variables) {
        //   if (!variables[key]) delete variables[key];
        // }
        if (option.column && process.variables && process.variables.daytime) {
        }
        this.option = option;
        this.form = variables;
        this.waiting = false;
      });
    },
    // 审核
    handleExamine(pass) {
      console.log(pass, 'pass---');
      this.submitLoading = true;
      console.log();
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          console.log(this.vars, 'vars');
          const variables = {};
          this.vars.forEach(v => {
            if (!this.validatenull(this.form[v])) {
              variables[v] = this.form[v];
              if (this.form[`$${v}`]) variables[`$${v}`] = this.form[`$${v}`];
            }
          });
          console.log(variables, 'variables');
          this.handleCompleteTask(pass, variables)
            .then(() => {
              this.$message.success('处理成功');
              this.handleCloseTag('/plugin/workflow/pages/process/todo');
            })
            .catch(() => {
              done();
              this.submitLoading = false;
            });
        } else {
          done();
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}
:deep(.el-input) {
  .el-input__wrapper {
    border: none !important;
  }
}
.demo-form-inline .el-input {
  // --el-input-width: 220px;
}

.demo-form-inline .el-select {
  // --el-select-width: 220px;
}
.el-form-item {
  width: 48%;
  margin-right: 0px;
  padding: 0 1%;
}
</style>
