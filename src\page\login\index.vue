<template>
  <div class="login-container" @keyup.enter="handleLogin">
    <!-- <div class="login-time">
      {{ time }}
    </div> -->
    <div class="login-weaper">
      <!-- <div class="login-left animate__animated animate__fadeInLeft">
        <img class="img" src="/img/logo.png" alt="" />
        <p class="title">{{ $t('login.info') }}</p>
      </div> -->
      <div class="login-border animate__animated animate__fadeInLeft">
        <div class="login-main">
          <p class="login-title" v-if="activeName != 'unify'">
            {{ $t('login.title') }}{{ website.title }}
            <top-lang></top-lang>
          </p>
          <userLogin v-if="activeName === 'user'"></userLogin>
          <codeLogin v-else-if="activeName === 'code'"></codeLogin>
          <thirdLogin v-else-if="activeName === 'third'"></thirdLogin>
          <registerLogin v-else-if="activeName === 'register'"></registerLogin>
          <redirect v-else-if="activeName === 'unify'"></redirect>
          <!-- <div class="login-menu">
            <el-link href="#" @click.stop="activeName = 'user'">{{
              $t('login.userLogin')
            }}</el-link>
            <el-link href="#" @click.stop="activeName = 'code'">{{
              $t('login.phoneLogin')
            }}</el-link>
            <el-link href="#" @click.stop="activeName = 'third'">{{
              $t('login.thirdLogin')
            }}</el-link>
            <el-link :href="website.oauth2.ssoUrl + website.oauth2.redirectUri">{{
              $t('login.ssoLogin')
            }}</el-link>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import userLogin from './userlogin.vue';
import registerLogin from './registerlogin.vue';
import codeLogin from './codelogin.vue';
import thirdLogin from './thirdlogin.vue';
import redirect from './redirect.vue';
import { mapGetters } from 'vuex';
import { validatenull } from '@/utils/validate';
import topLang from '@/page/index/top/top-lang.vue';
import { getQueryString, getTopUrl } from '@/utils/util';
import website from '@/config/website';
import { verifyToken } from '@/api/unfiy/unfiy.js';

export default {
  name: 'login',
  components: {
    userLogin,
    registerLogin,
    codeLogin,
    thirdLogin,
    topLang,
    redirect,
  },
  data() {
    return {
      website: website,
      time: '',
      // activeName: 'unify',
      activeName: 'user',

      // socialForm: {
      //   tenantId: '000000',
      //   source: '',
      //   code: '',
      //   state: '',
      // },
      socialForm: {
        tenantId: '126244',
        username: '',
      },
    };
  },
  watch: {
    $route() {
      this.handleLogin();
    },
  },
  created() {
    this.handleLogin();
    this.getTime();
  },
  mounted() {},
  computed: {
    ...mapGetters(['tagWel']),
  },
  props: [],
  methods: {
    getTime() {
      setInterval(() => {
        this.time = this.$dayjs().format('YYYY年MM月DD日 HH:mm:ss');
      }, 1000);
    },
    async handleLogin() {
      const topUrl = getTopUrl();
      console.log(topUrl, 'url');
      const redirectUrl = '/oauth/redirect/';
      const ssoCode = '?code=';
      this.socialForm.source = getQueryString('source');
      this.socialForm.code = getQueryString('code');
      this.socialForm.state = getQueryString('state');

      this.socialForm.code = getQueryString('code');
      if (validatenull(this.socialForm.source) && topUrl.includes(redirectUrl)) {
        let source = topUrl.split('?')[0];
        source = source.split(redirectUrl)[1];
        this.socialForm.source = source;
      }
      if (
        topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.source) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: '第三方系统登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.$store
          .dispatch('LoginBySocial', this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(redirectUrl)[0];
            //加载工作流路由集
            this.loadFlowRoutes();
            console.log('index111')
            // this.$router.push(this.tagWel);
            this.$router.push({
                path:'/plugin/workflow/pages/process/todo'
              });
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      } else if (
        !topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: '单点系统登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.$store
          .dispatch('LoginBySso', this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(ssoCode)[0];
            //加载工作流路由集
            this.loadFlowRoutes();
            // this.$router.push(this.tagWel);
            console.log(2323)
            this.$router.push({
                path:'/plugin/workflow/pages/process/todo'
              });
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      } else if (!validatenull(this.socialForm.code)) {
     
        const loading = this.$loading({
          lock: true,
          text: '系统登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        // let formDat1 = new FormData();
        // formDat1.append('token', this.socialForm.code);
        // console.log(formDat1, 'formDat1');
        // // let res = await verifyToken(formDat1);
        // token换取用户信息
        let res = await verifyToken({ token: this.socialForm.code });
        this.socialForm.username = res.data.data.userid;
        this.$store
          .dispatch('LoginByUnify', this.socialForm)
          .then(() => {
            //加载工作流路由集
            this.loadFlowRoutes();
   
            this.$router.push({
                name:'待办事宜',
                path:'/plugin/workflow/pages/process/todo'
              });
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      }
    },
    loadFlowRoutes() {
      this.$store.dispatch('FlowRoutes').then(() => {});
    },
  },
};
</script>
