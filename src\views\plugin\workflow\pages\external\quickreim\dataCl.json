{"ePdf": ["http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeAEdnSAADUoGhdlXA721.jpg", "http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeARxF6AACTh2k4FAE469.pdf", "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAEMQ7AAEgbrzc8lw689.png", "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAE5_7AACtM-Mj8Sc723.pdf", "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmATSEmAAGjO0Y0HWE662.pdf", "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aFGATZVIAAE1CgalNIc707.png"], "data": [{"invoiceDetail": [{"photoList": [{"iid": 1046190, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAKD-sAAZFAdGKLAs310.jpg", "aid": 428908}], "invoiceTypeNew": "3", "invoiceAmount": 533, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 2, "arrivalTime": "2024-08-08", "invoicer": "", "invoiceType": "0", "afterTaxAmount": 488.99, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "train", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "goOff": "2024-08-08", "pocketName": "南京出差", "taxAmount": 44.01, "departurePlace": "南京", "iid": 1046190, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "火车", "arrivalPlace": "北京", "certificateDtoList": [], "ePhotoList": [], "invoiceNumber": "", "currency": "CNY", "reimbursementName": "金小琪-240808-1", "subjectName": "差旅交通费", "invoicePurpose": "南京回北京", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 14:16", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046179, "linkIid": 1046190, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}], "reimbursement_amount": 533, "user_name": "金小琪", "actual_amount": 533, "sign": 1001, "initial_amount": 533, "reimbursement_date": "2024-08-08", "sid": 2, "voiceList": [1046190], "voiceCount": 1, "create_date": "2024-08-08 16:11:30", "reimbursement_purpose": "南京回北京", "link_type_id": 1046190, "revisability": false, "computerFlag": "0", "rbmDate": "", "business_subsidy": 120, "link_type": "0", "bdid": 1123687, "consolidated": 0, "month": "", "user_id": "jinxq", "business_standards": 60, "subject_name": "差旅交通费", "invoceAggregation": {"sumdaily": 0, "sumfounds": 0, "sumcustomer": 0, "sumcontract": 0, "sumopportunity": 0, "sumproject": 0, "sumstaff": 0}, "bid": 331813}, {"invoiceDetail": [{"photoList": [{"iid": 1046192, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aFGATZVIAAE1CgalNIc707.png", "aid": 428941}], "invoiceTypeNew": "1", "invoiceAmount": 16.44, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 20, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "other", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "pocketName": "南京出差", "taxAmount": 0, "departurePlace": "", "iid": 1046192, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "其他（地铁客车出租车等）", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1046192, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmATSEmAAGjO0Y0HWE662.pdf", "aid": 428910}], "invoiceNumber": "24317000000631780967", "currency": "CNY", "reimbursementName": "金小琪-240808-1", "subjectName": "市内交通费", "invoicePurpose": "客户地回酒店", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08 14:09", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 14:16", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046181, "linkIid": 1046192, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}], "reimbursement_amount": 16.44, "user_name": "金小琪", "actual_amount": 16.44, "sign": 1002, "initial_amount": 16.44, "reimbursement_date": "2024-08-08", "sid": 20, "voiceList": [1046192], "voiceCount": 1, "create_date": "2024-08-08 16:11:30", "reimbursement_purpose": "客户地回酒店", "link_type_id": 1046192, "revisability": false, "computerFlag": "0", "rbmDate": "", "business_subsidy": 120, "link_type": "0", "bdid": 1123688, "consolidated": 0, "month": "", "user_id": "jinxq", "business_standards": 60, "subject_name": "市内交通费", "invoceAggregation": {"sumdaily": 0, "sumfounds": 0, "sumcustomer": 0, "sumcontract": 0, "sumopportunity": 0, "sumproject": 0, "sumstaff": 0}, "bid": 331813}, {"invoiceDetail": [{"photoList": [{"iid": 1046238, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAEMQ7AAEgbrzc8lw689.png", "aid": 428964}], "invoiceTypeNew": "1", "invoiceAmount": 15.32, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 20, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "other", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "011002400111", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "pocketName": "南京出差", "taxAmount": 0, "departurePlace": "", "iid": 1046238, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "其他（地铁客车出租车等）", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1046238, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAE5_7AACtM-Mj8Sc723.pdf", "aid": 428965}], "invoiceNumber": "46118101", "currency": "CNY", "reimbursementName": "金小琪-240808-1", "subjectName": "市内交通费", "invoicePurpose": "客户地回公司", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08 14:44", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 14:45", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046227, "linkIid": 1046238, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}], "reimbursement_amount": 15.32, "user_name": "金小琪", "actual_amount": 15.32, "sign": 1003, "initial_amount": 15.32, "reimbursement_date": "2024-08-08", "sid": 20, "voiceList": [1046238], "voiceCount": 1, "create_date": "2024-08-08 16:11:30", "reimbursement_purpose": "客户地回公司", "link_type_id": 1046238, "revisability": false, "computerFlag": "0", "rbmDate": "", "business_subsidy": 120, "link_type": "0", "bdid": 1123686, "consolidated": 0, "month": "", "user_id": "jinxq", "business_standards": 60, "subject_name": "市内交通费", "invoceAggregation": {"sumdaily": 0, "sumfounds": 0, "sumcustomer": 0, "sumcontract": 0, "sumopportunity": 0, "sumproject": 0, "sumstaff": 0}, "bid": 331813}, {"invoiceDetail": [{"photoList": [{"iid": 1046337, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeAEdnSAADUoGhdlXA721.jpg", "aid": 429052}], "invoiceTypeNew": "1", "invoiceAmount": 24.62, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 20, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "other", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "011002400111", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "pocketName": "南京出差", "taxAmount": 0, "departurePlace": "", "iid": 1046337, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "其他（地铁客车出租车等）", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1046337, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeARxF6AACTh2k4FAE469.pdf", "aid": 429053}], "invoiceNumber": "40706826", "currency": "CNY", "reimbursementName": "金小琪-240808-1", "subjectName": "市内交通费", "invoicePurpose": "酒店到南京车站", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08 15:37", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 15:38", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046326, "linkIid": 1046337, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}], "reimbursement_amount": 24.62, "user_name": "金小琪", "actual_amount": 24.62, "sign": 1004, "initial_amount": 24.62, "reimbursement_date": "2024-08-08", "sid": 20, "voiceList": [1046337], "voiceCount": 1, "create_date": "2024-08-08 16:11:30", "reimbursement_purpose": "酒店到南京车站", "link_type_id": 1046337, "revisability": false, "computerFlag": "0", "rbmDate": "", "business_subsidy": 120, "link_type": "0", "bdid": 1123685, "consolidated": 0, "month": "", "user_id": "jinxq", "business_standards": 60, "subject_name": "市内交通费", "invoceAggregation": {"sumdaily": 0, "sumfounds": 0, "sumcustomer": 0, "sumcontract": 0, "sumopportunity": 0, "sumproject": 0, "sumstaff": 0}, "bid": 331813}, {"invoiceDetail": [{"photoList": [], "invoiceTypeNew": "3", "invoiceAmount": 33, "pid": 62417, "encryptCode": "", "type": "", "sheetsNum": 1, "resolution": 0, "subjectId": 7, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723651200000, "linkCode": "2311081442351140", "pocketLinkFlag": 0, "revisability": true, "vehicleCode": "", "invoiceStateList": [{"dvid": 1, "domainCode": "invoiceState", "domainValueDesc": "草稿", "domainValue": "0", "domainOrder": 1}, {"dvid": 2, "domainCode": "invoiceState", "domainValueDesc": "待验证", "domainValue": "1", "domainOrder": 2}, {"dvid": 3, "domainCode": "invoiceState", "domainValueDesc": "验证通过", "domainValue": "2", "domainOrder": 3}, {"dvid": 4, "domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "domainValue": "3", "domainOrder": 4}], "invoiceCode": "", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"dvid": 11, "domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "domainValue": "10100", "domainOrder": 1}, {"dvid": 12, "domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "domainValue": "10101", "domainOrder": 2}, {"dvid": 13, "domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "domainValue": "10102", "domainOrder": 3}, {"dvid": 14, "domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "domainValue": "10103", "domainOrder": 4}, {"dvid": 15, "domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "domainValue": "10104", "domainOrder": 5}, {"dvid": 16, "domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "domainValue": "10105", "domainOrder": 6}, {"dvid": 17, "domainCode": "invoiceType", "domainValueDesc": "定额发票", "domainValue": "10200", "domainOrder": 7}, {"dvid": 18, "domainCode": "invoiceType", "domainValueDesc": "机打发票", "domainValue": "10400", "domainOrder": 8}, {"dvid": 19, "domainCode": "invoiceType", "domainValueDesc": "出租车发票", "domainValue": "10500", "domainOrder": 9}, {"dvid": 20, "domainCode": "invoiceType", "domainValueDesc": "火车票", "domainValue": "10503", "domainOrder": 10}, {"dvid": 21, "domainCode": "invoiceType", "domainValueDesc": "客运汽车", "domainValue": "10505", "domainOrder": 11}, {"dvid": 22, "domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "domainValue": "10506", "domainOrder": 12}, {"dvid": 23, "domainCode": "invoiceType", "domainValueDesc": "过路费发票", "domainValue": "10507", "domainOrder": 13}, {"dvid": 24, "domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "domainValue": "10900", "domainOrder": 14}, {"dvid": 25, "domainCode": "invoiceType", "domainValueDesc": "其他", "domainValue": "00000", "domainOrder": 15}, {"dvid": 26, "domainCode": "invoiceType", "domainValueDesc": "国际小票", "domainValue": "20100", "domainOrder": 16}, {"dvid": 27, "domainCode": "invoiceType", "domainValueDesc": "飞机票", "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "taxAmount": 0, "pocketName": "默认票夹", "departurePlace": "", "ephotoList": [], "iid": 1049046, "feename": "", "invoiceState": 2, "buyerNumber": "", "vehicle": "", "arrivalPlace": "", "certificateDtoList": [], "invoiceNumber": "", "currency": "CNY", "reimbursementName": "孙忠全-240815-4", "subjectName": "住宿费", "invoicePurpose": "住宿费用", "invoiceLinkList": [{"dvid": 30, "domainCode": "linkType", "domainValueDesc": "关联客户", "domainValue": "link_customer", "domainOrder": 1}, {"dvid": 31, "domainCode": "linkType", "domainValueDesc": "关联销售机会", "domainValue": "link_opportunity", "domainOrder": 2}, {"dvid": 32, "domainCode": "linkType", "domainValueDesc": "关联投标", "domainValue": "link_founds", "domainOrder": 3}, {"dvid": 33, "domainCode": "linkType", "domainValueDesc": "关联项目", "domainValue": "link_project", "domainOrder": 4}, {"dvid": 34, "domainCode": "linkType", "domainValueDesc": "关联日志", "domainValue": "link_daily", "domainOrder": 5}, {"dvid": 35, "domainCode": "linkType", "domainValueDesc": "关联合同", "domainValue": "link_contract", "domainOrder": 6}, {"dvid": 36, "domainCode": "linkType", "domainValueDesc": "关联人员", "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-15", "invoiceLinkState": 1, "userName": "孙忠全", "userId": "sunzhongq", "invoiceSpecial": 0, "createTime": "2024-08-15 10:51", "dayNum": 3, "linkType": "link_founds", "invoiceLinkInfo": {"linkTypeName": "关联投标", "linkCode": "2311081442351140", "linkId": 1049035, "linkIid": 1049046, "linkType": "link_founds", "linkName": "2023年智慧海淀专项-海淀区儿童福利院（综合福利院）信息化建设项目其他运行维护服务采购项目", "linkDate": "", "linkRemarks": ""}}], "reimbursement_amount": 33, "user_name": "孙忠全", "actual_amount": 33, "sign": 1002, "initial_amount": 33, "reimbursement_date": "2024-08-15", "sid": 7, "voiceList": [1049046], "voiceCount": 1, "create_date": {"dayOfWeek": "THURSDAY", "month": "AUGUST", "hour": 10, "year": 2024, "dayOfMonth": 15, "dayOfYear": 228, "monthValue": 8, "nano": 0, "chronology": {"calendarType": "iso8601", "id": "ISO"}, "minute": 51, "second": 42}, "reimbursement_purpose": "住宿费用", "link_type_id": 1049046, "revisability": true, "computerFlag": "0", "rbmDate": "", "business_subsidy": 12000, "link_type": "0", "bdid": 1126680, "consolidated": 0, "month": "", "user_id": "sunzhongq", "business_standards": 150, "subject_name": "住宿费", "invoceAggregation": {"sumdaily": 0, "sumfounds": 0, "sumcustomer": 0, "sumcontract": 0, "sumopportunity": 0, "sumproject": 0, "sumstaff": 0}, "bid": 332474}], "proportion": "", "amountLinkSelf": 0, "isPostSaleProject": false, "presale": false, "currentByUserId": {"current": 1000, "transfer": 0, "subtract": 2000, "preceding": 1000, "isGJ": false}, "LogList": [], "assigneeMessage": [{"processInstanceId": "20991329", "messageParts": ["同意。"], "action": "AddComment", "fullMessage": "同意。", "fullMessageBytes": "5ZCM5oSP44CC", "persistentState": "org.activiti.engine.impl.persistence.entity.CommentEntity", "id": "20998495", "time": 1723124328000, "message": "同意。", "type": "comment", "userId": "安琪", "taskId": "20991351"}, {"processInstanceId": "20991329", "messageParts": ["同意。"], "action": "AddComment", "fullMessage": "同意。", "fullMessageBytes": "5ZCM5oSP44CC", "persistentState": "org.activiti.engine.impl.persistence.entity.CommentEntity", "id": "21090115", "time": 1723524676000, "message": "同意。", "type": "comment", "userId": "王健", "taskId": "20998500"}, {"processInstanceId": "20991329", "messageParts": ["同意。"], "action": "AddComment", "fullMessage": "同意。", "fullMessageBytes": "5ZCM5oSP44CC", "persistentState": "org.activiti.engine.impl.persistence.entity.CommentEntity", "id": "21112645", "time": 1724382202000, "message": "同意。", "type": "comment", "userId": "王佺", "taskId": "21090118"}, {"processInstanceId": "20991329", "messageParts": ["同意(其他审批环节已同意,系统默认通过)"], "action": "AddComment", "fullMessage": "同意(其他审批环节已同意,系统默认通过)", "fullMessageBytes": "5ZCM5oSPKOWFtuS7luWuoeaJueeOr+iKguW3suWQjOaEjyzns7vnu5/pu5jorqTpgJrov4cp", "persistentState": "org.activiti.engine.impl.persistence.entity.CommentEntity", "id": "21112650", "time": 1724382202000, "message": "同意(其他审批环节已同意,系统默认通过)", "type": "comment", "userId": "王佺", "taskId": "21112649"}], "approvalPerson": ["安琪", "王健", "万云涛", "吕兴海"], "showSales": false, "rbtReimbusement": {"newType": 0, "marketingDirector": "", "isElectronic": 0, "userCode": "销售三部-5617", "employeeNumber": "5617", "businessPlace": "南京", "isLinked": 0, "reimbursementType": "zl", "result": 0, "revocation": 0, "reimbursementDesc": "南京投标出差", "businessSubsidy": 120, "businessStandards": 60, "client": 1, "currency": "CNY", "reimbursementAmount": 709.38, "reimbursementTypeName": "差旅", "reimbursementName": "金小琪-240808-1", "approvalStage": "常务副总裁", "marketingDirectorName": "", "approvalState": 1, "userName": "金小琪", "currentAssignee": "周曙", "userId": "jinxq", "createTime": 1723104720000, "processInstId": "20991329", "submissionorder": 1, "businessDay": 2, "position": "销售助理", "bid": 331813, "contracPlace": "1"}, "userState": 1, "isArea": false, "redisKey": "jinxq:240808-1", "invoiceMessage": [{"photoList": [{"iid": 1046190, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmAKD-sAAZFAdGKLAs310.jpg", "aid": 428908}], "invoiceTypeNew": "3", "invoiceAmount": 533, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 2, "arrivalTime": "2024-08-08", "invoicer": "", "invoiceType": "0", "afterTaxAmount": 488.99, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "train", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "goOff": "2024-08-08", "pocketName": "南京出差", "taxAmount": 44.01, "departurePlace": "南京", "iid": 1046190, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "火车", "arrivalPlace": "北京", "certificateDtoList": [], "ePhotoList": [], "invoiceNumber": "", "currency": "CNY", "reimbursementName": "金小琪-240808-1", "subjectName": "差旅交通费", "invoicePurpose": "南京回北京", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 14:16", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046179, "linkIid": 1046190, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}, {"photoList": [{"iid": 1046192, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aFGATZVIAAE1CgalNIc707.png", "aid": 428941}], "invoiceTypeNew": "1", "invoiceAmount": 16.44, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 20, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "other", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "pocketName": "南京出差", "taxAmount": 0, "departurePlace": "", "iid": 1046192, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "其他（地铁客车出租车等）", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1046192, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0YtmATSEmAAGjO0Y0HWE662.pdf", "aid": 428910}], "invoiceNumber": "24317000000631780967", "currency": "CNY", "reimbursementName": "金小琪-240808-1", "subjectName": "市内交通费", "invoicePurpose": "客户地回酒店", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08 14:09", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 14:16", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046181, "linkIid": 1046192, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}, {"photoList": [{"iid": 1046238, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAEMQ7AAEgbrzc8lw689.png", "aid": 428964}], "invoiceTypeNew": "1", "invoiceAmount": 15.32, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 20, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "other", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "011002400111", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "pocketName": "南京出差", "taxAmount": 0, "departurePlace": "", "iid": 1046238, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "其他（地铁客车出租车等）", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1046238, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DD/wKiwDGa0aXSAE5_7AACtM-Mj8Sc723.pdf", "aid": 428965}], "invoiceNumber": "46118101", "currency": "CNY", "reimbursementName": "金小琪-240808-1", "subjectName": "市内交通费", "invoicePurpose": "客户地回公司", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08 14:44", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 14:45", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046227, "linkIid": 1046238, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}, {"photoList": [{"iid": 1046337, "photo_type": "1", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeAEdnSAADUoGhdlXA721.jpg", "aid": 429052}], "invoiceTypeNew": "1", "invoiceAmount": 24.62, "pid": 65434, "encryptCode": "", "type": "", "resolution": 0, "sheetsNum": 1, "subjectId": 20, "invoicer": "", "invoiceType": "0", "afterTaxAmount": 0, "happenTime": 1723046400000, "linkCode": "anqi", "pocketLinkFlag": 0, "revisability": false, "vehicleCode": "other", "invoiceStateList": [{"domainCode": "invoiceState", "domainValueDesc": "草稿", "dvid": 1, "domainValue": "0", "domainOrder": 1}, {"domainCode": "invoiceState", "domainValueDesc": "待验证", "dvid": 2, "domainValue": "1", "domainOrder": 2}, {"domainCode": "invoiceState", "domainValueDesc": "验证通过", "dvid": 3, "domainValue": "2", "domainOrder": 3}, {"domainCode": "invoiceState", "domainValueDesc": "验证拒绝", "dvid": 4, "domainValue": "3", "domainOrder": 4}], "invoiceCode": "011002400111", "buyer": "", "checkCode": "", "consolidated": 0, "invoiceTypeList": [{"domainCode": "invoiceType", "domainValueDesc": "增值税专用发票", "dvid": 11, "domainValue": "10100", "domainOrder": 1}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票", "dvid": 12, "domainValue": "10101", "domainOrder": 2}, {"domainCode": "invoiceType", "domainValueDesc": "增值税电子普通发票", "dvid": 13, "domainValue": "10102", "domainOrder": 3}, {"domainCode": "invoiceType", "domainValueDesc": "增值税普通发票(卷票)", "dvid": 14, "domainValue": "10103", "domainOrder": 4}, {"domainCode": "invoiceType", "domainValueDesc": "机动车销售统一发票", "dvid": 15, "domainValue": "10104", "domainOrder": 5}, {"domainCode": "invoiceType", "domainValueDesc": "二手车销售统一发票", "dvid": 16, "domainValue": "10105", "domainOrder": 6}, {"domainCode": "invoiceType", "domainValueDesc": "定额发票", "dvid": 17, "domainValue": "10200", "domainOrder": 7}, {"domainCode": "invoiceType", "domainValueDesc": "机打发票", "dvid": 18, "domainValue": "10400", "domainOrder": 8}, {"domainCode": "invoiceType", "domainValueDesc": "出租车发票", "dvid": 19, "domainValue": "10500", "domainOrder": 9}, {"domainCode": "invoiceType", "domainValueDesc": "火车票", "dvid": 20, "domainValue": "10503", "domainOrder": 10}, {"domainCode": "invoiceType", "domainValueDesc": "客运汽车", "dvid": 21, "domainValue": "10505", "domainOrder": 11}, {"domainCode": "invoiceType", "domainValueDesc": "航空运输电子客票行程单", "dvid": 22, "domainValue": "10506", "domainOrder": 12}, {"domainCode": "invoiceType", "domainValueDesc": "过路费发票", "dvid": 23, "domainValue": "10507", "domainOrder": 13}, {"domainCode": "invoiceType", "domainValueDesc": "可报销其他发票", "dvid": 24, "domainValue": "10900", "domainOrder": 14}, {"domainCode": "invoiceType", "domainValueDesc": "其他", "dvid": 25, "domainValue": "00000", "domainOrder": 15}, {"domainCode": "invoiceType", "domainValueDesc": "国际小票", "dvid": 26, "domainValue": "20100", "domainOrder": 16}, {"domainCode": "invoiceType", "domainValueDesc": "飞机票", "dvid": 27, "domainValue": "10508", "domainOrder": 17}], "month": "", "cardId": "", "pocketName": "南京出差", "taxAmount": 0, "departurePlace": "", "iid": 1046337, "feename": "", "invoiceState": 1, "buyerNumber": "", "vehicle": "其他（地铁客车出租车等）", "arrivalPlace": "", "certificateDtoList": [], "ePhotoList": [{"iid": 1046337, "photo_type": "4", "name": "电子发票附件", "invoice_photo_url": "http://192.168.176.13:8888/group2/M00/0D/DE/wKiwDGa0deeARxF6AACTh2k4FAE469.pdf", "aid": 429053}], "invoiceNumber": "40706826", "currency": "CNY", "reimbursementName": "金小琪-240808-1", "subjectName": "市内交通费", "invoicePurpose": "酒店到南京车站", "invoiceLinkList": [{"domainCode": "linkType", "domainValueDesc": "关联客户", "dvid": 30, "domainValue": "link_customer", "domainOrder": 1}, {"domainCode": "linkType", "domainValueDesc": "关联销售机会", "dvid": 31, "domainValue": "link_opportunity", "domainOrder": 2}, {"domainCode": "linkType", "domainValueDesc": "关联投标", "dvid": 32, "domainValue": "link_founds", "domainOrder": 3}, {"domainCode": "linkType", "domainValueDesc": "关联项目", "dvid": 33, "domainValue": "link_project", "domainOrder": 4}, {"domainCode": "linkType", "domainValueDesc": "关联日志", "dvid": 34, "domainValue": "link_daily", "domainOrder": 5}, {"domainCode": "linkType", "domainValueDesc": "关联合同", "dvid": 35, "domainValue": "link_contract", "domainOrder": 6}, {"domainCode": "linkType", "domainValueDesc": "关联人员", "dvid": 36, "domainValue": "link_staff", "domainOrder": 7}], "invoiceDate": "2024-08-08 15:37", "invoiceLinkState": 1, "userName": "金小琪", "userId": "jinxq", "invoiceSpecial": 0, "createTime": "2024-08-08 15:38", "linkType": "link_staff", "invoiceLinkInfo": {"linkTypeName": "关联人员", "linkCode": "anqi", "linkId": 1046326, "linkIid": 1046337, "linkType": "link_staff", "linkDate": "", "linkName": "安琪", "linkRemarks": ""}}], "kjrcwb": 0, "afterSale": false, "certificatesMessage": [], "consolidatedInvoices": []}