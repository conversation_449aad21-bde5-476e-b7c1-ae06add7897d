<template>
  <nf-container>
    <el-skeleton :loading="waiting" avatar :rows="8">
      <el-affix position="top" :offset="110">
        <div class="header">
          <h3>{{ process.processDefinitionName }}</h3>
          <div style="display: flex">
            <!-- <nf-theme
              v-if="process.status != 'todo'"
              v-model="theme"
              :theme-list="themeList"
            ></nf-theme>
            <nf-form-variable :process-ins-id="process.processInstanceId"></nf-form-variable> -->
          </div>
        </div>
      </el-affix>
      <el-tabs v-model="activeName">
        <el-tab-pane label="申请信息" name="first">
          <el-card shadow="never">
            <div id="printBody" :class="process.status != 'todo' ? `nf-theme-custom` : ''">
              <!-- <nf-form
                v-if="
                  option &&
                  ((option.column && option.column.length > 0) ||
                    (option.group && option.group.length > 0))
                "
                v-model="form"
                :style="themeCustomStyle"
                ref="form"
                v-model:defaults="defaults"
                :option="option"
                :upload-preview="handleUploadPreview"
              >
              </nf-form> -->

              <div class="app-container">
                <div class="content">
                  <!-- 基本信息 -->
                  <div class="ordinary">
                    <div>基本信息</div>
                  </div>
                  <div class="info" style="position: relative">
                    <ul>
                      <li>
                        <div><span>借款单名称</span>:</div>
                        <span>{{ travelinfo.loanName }}</span
                        >&nbsp;<el-tag size="mini" type="success">{{
                          getCurrencyVal(travelinfo.currency)
                        }}</el-tag>
                      </li>
                      <li>
                        <div><span>借款人</span>:</div>
                        <span>{{ travelinfo.userName }}</span>
                      </li>
                      <li>
                        <div><span>部门</span>:</div>
                        <span>{{ travelinfo.department }}</span>
                        <!-- <span>{{travelinfo.contracPlace}}{{company}}{{num}}</span> -->
                      </li>
                      <li>
                        <div><span>借款用途</span>:</div>
                        <span>{{ travelinfo.purpose }}</span>
                      </li>
                      <li>
                        <div><span>借款总额</span>:</div>
                        <span style="font-weight: 800; color: red"
                          >{{ toThousands(travelinfo.loanAmount)
                          }}{{ getCurrencyUnit(travelinfo.currency) }}</span
                        >
                      </li>
                      <li>
                        <div><span>支付方式</span>:</div>
                        <span>{{ travelinfo.payType }}</span>
                      </li>
                      <li v-show="!!travelinfo.payCompany">
                        <div><span>付款公司</span>:</div>
                        <span>{{ travelinfo.payCompany }}</span>
                      </li>
                      <li v-show="travelinfo.fromBank">
                        <div><span>从</span>:</div>
                        <span>{{ travelinfo.fromBank }}</span>
                      </li>
                      <li v-show="travelinfo.toBank">
                        <div><span>到</span>:</div>
                        <span>{{ travelinfo.toBank }}</span>
                      </li>
                      <li v-show="travelinfo.bankBranch">
                        <div><span>银行</span>:</div>
                        <span>{{ travelinfo.bankBranch }}</span>
                      </li>
                      <li v-show="travelinfo.bankCardNo">
                        <div><span>卡号</span>:</div>
                        <span>{{ travelinfo.bankCardNo }}</span>
                      </li>
                      <li v-show="travelinfo.bankName">
                        <div><span>开户名称</span>:</div>
                        <span>{{ travelinfo.bankName }}</span>
                      </li>
                      <li v-show="travelinfo.cashier">
                        <div><span>出纳员</span>:</div>
                        <span>{{ travelinfo.cashier }}</span>
                      </li>
                      <li>
                        <div><span>创建日期</span>:</div>
                        <!-- <span>{{ formatTime(travelinfo.borrowTime, 'YMDHM') }}</span> -->
                        <span>{{ travelinfo.borrowTime}}</span>
                      </li>
                      <li>
                        <div><span>备注</span>:</div>
                        <span>{{ travelinfo.remark }}</span>
                      </li>
                    </ul>
                  </div>
                  <!-- 项目日志 -->
                  <div class="ordinary" v-if="isResult">
                    <div>项目日志</div>
                  </div>

                  <div class="detailTable" v-if="isResult">
                    <el-table border style="width: 100%" :data="resultList">
                      <el-table-column label="日志时间">
                        <template #default="scope">
                          <span>{{ scope.row.DiaryDate }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="开始时间">
                        <template #default="scope">
                          <span>{{ scope.row.StartTime }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="结束时间">
                        <template #default="scope">
                          <span>{{ scope.row.EndDate }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="工作位置">
                        <template #default="scope">
                          <span>{{ scope.row.WorkPlace }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="工作内容">
                        <template #default="scope">
                          <!-- <el-tooltip class="item"> -->
                          <span>{{ scope.row.Content }}</span>
                          <!-- </el-tooltip> -->
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>

              <el-card
                shadow="never"
                style="margin-top: 10px"
                header="流转信息"
                :body-style="{ padding: '20px 0' }"
                v-if="isPrintShow"
              >
                <nf-flow :flow-list="flow"></nf-flow>
              </el-card>
            </div>
          </el-card>
          <el-card shadow="never" style="margin-top: 20px" v-if="process.status == 'todo'">
            <nf-examine-form
              ref="examineForm"
              v-model:comment="comment"
              :process="process"
              @user-select="handleUserSelect"
            ></nf-examine-form>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流转信息" name="second">
          <el-card shadow="never" style="margin-top: 5px">
            <nf-flow :flow-list="flow"></nf-flow>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="流程跟踪" name="third">
          <template v-if="activeName == 'third'">
            <el-card shadow="never" style="margin-top: 5px">
              <nf-design ref="bpmn" style="height: 500px" :options="bpmnOption"></nf-design>
            </el-card>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-skeleton>
    <!-- 底部按钮 -->
    <nf-button
      :loading="submitLoading"
      :button-list="buttonList"
      :process="process"
      :comment="comment"
      @examine="handleExamine"
      @user-select="handleUserSelect"
      @print="handlePrint"
      @rollback="handleRollbackTask"
      @terminate="handleTerminateProcess"
      @withdraw="handleWithdrawTask"
    ></nf-button>
    <!-- 人员选择弹窗 -->
    <nf-user-select
      ref="user-select"
      :check-type="checkType"
      :default-checked="defaultChecked"
      :user-select-type="userSelectType"
      @onConfirm="handleUserSelectConfirm"
    ></nf-user-select>
  </nf-container>
</template>

<script>
import NfExamineForm from '../../../components/nf-exam-form/index.vue';
import NfButton from '../../../components/nf-button/index.vue';
import NfFlow from '../../../components/nf-flow/index.vue';
import NfTheme from '../../../components/nf-theme/index.vue';
import NfFormVariable from '../../../components/nf-form-variable/index.vue';
import NfUserSelect from '../../../components/nf-user-select/index.vue';

import exForm from '../../../mixins/ex-form';
import theme from '../../../mixins/theme';
import { getLoanDetail, getWorkDiary, getCurrencyData } from '@/api/reim/reim.js';
export default {
  mixins: [exForm, theme],
  components: { NfUserSelect, NfExamineForm, NfButton, NfFlow, NfTheme, NfFormVariable },
  watch: {
    '$route.query.p': {
      handler(val) {
        if (val) {
          const param = JSON.parse(window.atob(val));
          const { taskId, processInsId, serialNumber } = param;
          console.log(param, 'param-------------------');
          if ((taskId && processInsId) || processInsId) this.getDetail(taskId, processInsId);
          if (serialNumber) this.approvalResult(serialNumber);
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      option: {
        column: [
          {
            type: 'input',
            label: '金额',
            span: 12,
            display: true,
            prop: 'money',
            readonly: true,
          },
          {
            type: 'input',
            label: '流水号',
            span: 12,
            display: true,
            prop: 'reimId',
            readonly: true,
          },
        ],
      },
      vars: ['reimId', 'userName'], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      // 基本信息
      travelinfo: {},

      // 项目日志
      isResult: 0,
      resultList: [], //需要单独调接口
      isShow: false, // 是否财务复核
      // 审批日志
      journalData: [
        {
          userId: '安琪',
          time: 1723124328000,
          fullMessage: '同意。',
        },
        {
          userId: '王健',
          time: 1723524676000,
          fullMessage: '同意。',
        },
        {
          userId: '王佺',
          time: 1724382202000,
          fullMessage: '同意。',
        },
        {
          userId: '王佺',
          time: 1724382202000,
          fullMessage: '同意(其他审批环节已同意,系统默认通过)',
        },
      ], //需要单独调接口
      // 币种
      currencylist: [
        {
          unit: '元',
          code: '¥',
          currency: 'CNY',
          disabled: false,
          label: '人民币',
        },
        {
          unit: '港元',
          code: 'HK$',
          currency: 'HKD',
          disabled: false,
          label: '港币',
        },
        {
          unit: '美元',
          code: '$',
          currency: 'USD',
          disabled: true,
          label: '美元',
        },
        {
          unit: '新加坡元',
          code: 'S$',
          currency: 'SGD',
          disabled: true,
          label: '新加坡元',
        },
        {
          unit: '英镑',
          code: '￡',
          currency: 'GBP',
          disabled: true,
          label: '英镑',
        },
        {
          unit: '雷亚尔',
          code: 'R$',
          currency: 'BRL',
          disabled: true,
          label: '雷亚尔',
        },
      ], //需要单独调接口
      // 财务详情接口返回数据
      result: {},
    };
  },
  mounted() {
    this.getCurrencyList();
  },
  methods: {
    // 前分为显示逗号
    toThousands: function (num) {
      num = num * 1;
      var num_str = num.toFixed(2).split('.')[0];
      return (
        (num_str || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') +
        '.' +
        num.toFixed(2).split('.')[1]
      );
    },
    //获取币种
    getCurrencyUnit(val) {
      const foundOption = this.currencylist.find(option => option.currency == val);
      return foundOption ? foundOption.unit : '元';
    },
    //获取币种
    getCurrency(val) {
      const foundOption = this.currencylist.find(option => option.currency == val);
      return foundOption ? foundOption.code : '￥';
    },
    getCurrencyVal(val) {
      const foundOption = this.currencylist.find(option => option.currency == val);
      return foundOption ? foundOption.label : '人民币';
    },
    getCurrencyList() {
      getCurrencyData({ userName: this.$store.getters.userInfo.user_id }).then(res => {
        if (res.data.code == 1) {
          this.currencylist = res.data.data.base;
        }
      });
    },
    //时间戳转换为普通格式
    formatTime(value, type) {
      let dataTime = '';
      let data = new Date();
      data.setTime(value);
      let year = data.getFullYear();
      let month = data.getMonth() + 1;
      let day = data.getDate();
      let hour = data.getHours();
      let minute = data.getMinutes();
      let second = data.getSeconds();

      month < 10 ? (month = '0' + month) : month;
      day < 10 ? (day = '0' + day) : day;
      hour < 10 ? (hour = '0' + hour) : hour;
      minute < 10 ? (minute = '0' + minute) : minute;
      second < 10 ? (second = '0' + second) : second;

      if (type == 'YMD') {
        dataTime = year + '-' + month + '-' + day;
      } else if (type == 'NYR') {
        dataTime = year + '年' + month + '月' + day + '日';
      } else if (type == 'YMDHMS') {
        dataTime = year + '-' + month + '-' + day + '  ' + hour + ':' + minute + ':' + second;
      } else if (type == 'YMDHM') {
        dataTime = year + '-' + month + '-' + day + '  ' + hour + ':' + minute;
      } else if (type == 'HMS') {
        dataTime = hour + ':' + minute + ':' + second;
      } else if (type == 'YM') {
        dataTime = year + '-' + month + '-';
      }
      return dataTime; // 将格式化后的字符串输出到前端显示
    },
    // 时间格式化
    formatDate(date, fmt) {
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
      }
      let o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
      };
      for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
          let str = o[k] + '';
          fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str));
        }
      }
      return fmt;
    },

    formatDate1(time) {
      var date = new Date(time);
      return formatDate(date, 'yyyy-MM-dd');
    },
    // 财务审批表单详情
    approvalResult(serialNumber) {
      getLoanDetail(serialNumber).then(result => {
        this.travelinfo = result.data.data.rbtLoan;

        this.waiting = false;
        const userId = {
          userId: this.travelinfo.userId,
        };
        this.workDiary(this.travelinfo.bid, this.travelinfo.userId);
      });
    },

    // // 工作日志
    // workDiary(bid, userId) {
    //   let data = {
    //     bid: bid,
    //     userId: userId,
    //   };
    //   getWorkDiary(data).then(result => {
    //     console.log('工作日志....', result.data.data);
    //     if (result.data.data) {
    //       this.isResult = 1;
    //       this.resultList = result.data.data;
    //     } else {
    //       this.isResult = 0;
    //     }
    //   });
    // },

    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(res => {
        const { process, form } = res;
        const { variables, status } = process;

        let { taskForm } = form;
        const option = this.option;
        option.menuBtn = false;
        let { column, group } = option;
        if (taskForm && taskForm.length > 0) {
          const columnFilter = this.filterAvueColumn(column, taskForm, true);
          console.log(columnFilter, 'columnFilter');
          column = columnFilter.column;
          let vars = columnFilter.vars || [];

          const groupArr = [];
          if (group && group.length > 0) {
            // 处理group
            group.forEach(gro => {
              const groupFilter = this.filterAvueColumn(gro.column, taskForm, true);
              gro.column = groupFilter.column;
              vars = vars.concat(groupFilter.vars);
              if (gro.column.length > 0) groupArr.push(gro);
            });
          }
          group = groupArr;
          // this.vars = vars;
        }

        if (status != 'todo') {
          // 已办，删除字段默认值
          option.detail = true;
          if (column && column.length > 0) {
            // 处理column
            column.forEach(col => {
              if (col.type == 'dynamic')
                col.children.column.forEach(cc => {
                  delete cc.value;
                });
              delete col.value;
            });
          }

          if (group && group.length > 0) {
            // 处理group
            group.forEach(gro => {
              if (gro.column && gro.column.length > 0) {
                gro.column.forEach(col => {
                  if (col.type == 'dynamic')
                    col.children.column.forEach(cc => {
                      delete cc.value;
                    });
                  delete col.value;
                });
              }
            });
          }
        }
        option.column = column;
        option.group = group;

        for (let key in variables) {
          if (!variables[key]) delete variables[key];
        }

        if (option.column && process.variables && process.variables.serialNumber) {
          option.column.unshift({
            label: '流水号',
            prop: 'serialNumber',
            span: 24,
            detail: true,
          });
        }

        this.option = option;
        this.form = variables;
        this.waiting = false;
      });
    },
    // 审核
    handleExamine(pass) {
      this.submitLoading = true;
      console.log(this.vars, 'this.vars');
      console.log(this.form, 'this.form');
      // this.$refs.form.validate((valid, done) => {
      // if (valid) {
      const variables = {};
      this.vars.forEach(v => {
        if (!this.validatenull(this.form[v])) {
          variables[v] = this.form[v];
          if (this.form[`$${v}`]) variables[`$${v}`] = this.form[`$${v}`];
        }
      });
      console.log(this.$store.getters.userInfo, '提交表单');
      variables.userName = this.$store.getters.userInfo.user_id;
      console.log(pass, variables);

      this.handleCompleteTask(pass, variables)
        .then(() => {
          this.$message.success('处理成功');
          this.handleCloseTag('/plugin/workflow/pages/process/todo');
        })
        .catch(() => {
          done();
          this.submitLoading = false;
        });
      // } else {
      //   done();
      //   this.submitLoading = false;
      // }
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px 10px 0;
}

// .header {
//   height: 50px;
//   display: flex;
//   align-items: center;
//   background: #fff;
//   border-top: solid 1px #e6e6e6;
//   position: relative;
//   .goBack {
//     cursor: pointer;
//     span {
//       color: #297cba;
//       &:nth-child(2) {
//         font-size: 16px;
//         border-right: 1px solid #ccc;
//         padding-right: 10px;
//       }
//       i {
//         color: #297cba;
//         font-size: 16px;
//       }
//     }
//   }
//   .check {
//     padding-left: 10px;
//     font-size: 16px;
//   }
// }
.dialog {
  .info {
    width: 100%;
    padding: 0 30%;
    box-sizing: border-box;
    margin-top: -30px;
    margin-bottom: -30px;
    p {
      display: flex;
      align-items: center;
      margin: 10px 0;
      .lable-info {
        width: 110px;
      }
      .info-right {
        display: inline-block;
        flex: 1;
      }
    }
  }
}
// .dialog-footer{
//   margin-top: -30px !important;
// }

.detail {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.info-right-active {
  color: red;
}
.cpu-box {
  width: 100%;
  padding: 0 200px 0 15px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.zlinfo {
  width: 100%;
  display: flex;
  padding: 10px 10px;
  box-sizing: border-box;
  align-items: center;
  font-size: 16px;
  color: #333;
  border: 1px solid #eee;
  margin: 20px 0;
  .spn,
  p {
    display: inline-block;
    flex: 1;
  }
  .setspn {
    color: #fff;
    padding: 7px 17px;
    border-radius: 5px;
    display: inline-block;
    background: #409eff;
  }
  .onsetspn {
    background: #e6a23c;
    margin-right: 15px;
  }
}
.app-container {
  width: 96%;
  height: 100%;
  // margin: 20px auto;
  padding: 0;
  background: #fff;
}
.content {
  // padding: 0 20px;
  .essentialInfo {
    width: 100%;
    font-size: 12px;
    font-weight: 600;
    color: #000;
    div {
      height: 40px;
      line-height: 40px;
    }
  }
  .info {
    ul {
      padding: 0;
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #ebeef5;
      li {
        width: 33.3%;
        padding: 0;
        margin: 0;
        list-style-type: none;
        display: flex;
        align-items: center;
        font-size: 16px;
        & > div {
          padding-left: 20px;
          font-size: 16px;
          display: flex;
          width: 100px;
          padding: 10px;
          & > span {
            text-align-last: justify;
            flex: 1;
            display: inline-block;
          }
        }
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5),
        &:nth-child(6),
        &:nth-child(7),
        &:nth-child(8),
        &:nth-child(9),
        &:nth-child(10),
        &:nth-child(11),
        &:nth-child(12) {
          border-bottom: 1px solid #ebeef5;
        }
        &:last-child {
          width: 100%;
          border-top: 1px solid #ebeef5;
          div {
            padding: 10px;
          }
        }
      }
    }
  }
  .ordinary {
    font-size: 12px;
    color: #000;
    display: flex;
    // margin: 20px 0;
    div {
      padding: 12px 15px;
      border-radius: 5px;
      font-weight: 600;
      margin-right: 20px;
    }
    .ordinaryActive {
      color: #fff;

      background: #409eff;
    }
  }
  .computer_block {
    display: flex;
    justify-content: space-between;
    width: 60%;
    margin-left: 15px;
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 30px;
    span {
      color: red;
    }
  }
  .approvalLog {
    width: 100%;
    font-size: 12px;
    font-weight: 600;
    color: #000;
    div {
      height: 40px;
      line-height: 40px;
    }
  }
}
.select {
  span {
    color: #000;
    font-size: 12px;
    font-weight: 600;
  }
}
.afterSale {
  .select {
    display: flex;
    align-items: center;
    margin: 15px 0;
    span {
      color: #000;
      font-size: 12px;
      font-weight: 600;
    }
  }
}
.ordinaryDetails {
  border: 1px #ebeef5 solid;
  border-bottom: 0;
  &:last-child {
    border-bottom: 0;
  }
  .el-tag.el-tag--warning {
    background-color: #ffe6c2;
    border-color: #ffe9d6;
    color: #bf7507;
  }
}
.ordinaryDetail {
  margin-bottom: 15px;
}
.result {
  width: 95%;
  margin: 20px auto;
  .btn {
    width: 90px;
  }
}
.purpose {
  display: inline-block;
  width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.active {
  border: 1px solid blue !important;
  background: blue;
}
.taxBox {
  display: inline-block;
  width: 10px;
  height: 10px;
  border: 1px solid #ccc;
}
.reimbursementDesc {
  display: inline-block;
  width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dz_icon {
  right: -10px;
  top: -50px;
  position: absolute;
  width: 80px;
  transform: rotate(20deg);
}
</style>
